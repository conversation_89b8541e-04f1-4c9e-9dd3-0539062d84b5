<div class="member-details-page">
  <!-- Contenu principal -->
  <div class="page-content" *ngIf="!loading; else loadingTemplate">
    <div class="member-details-container" *ngIf="member">
      <!-- En-tête -->
      <div class="details-header">
        <div class="member-info">
          <div class="member-avatar">
            {{ member.firstName.charAt(0) }}{{ member.lastName.charAt(0) }}
          </div>
          <div class="member-basic-info">
            <h1 class="member-name">{{ member.firstName }} {{ member.lastName }}</h1>
            <p class="member-id">#{{ member.id }}</p>
            <div class="member-badges">
              <nz-tag [nzColor]="getTypeColor(member.memberType)">
                {{ getTypeText(member.memberType) }}
              </nz-tag>
              <nz-tag [nzColor]="getStatusColor(member.status)">
                {{ getStatusText(member.status) }}
              </nz-tag>
            </div>
          </div>
        </div>
        <div class="header-actions">
          <button nz-button nzType="default" (click)="goBack()">
            <nz-icon nzType="arrow-left" nzTheme="outline"></nz-icon>
            <span>Retour</span>
          </button>
          <button nz-button nzType="primary" (click)="editMember()">
            <nz-icon nzType="edit" nzTheme="outline"></nz-icon>
            <span>Modifier</span>
          </button>
        </div>
      </div>

      <!-- Contenu des détails -->
      <div class="details-content">
        <!-- Informations personnelles -->
        <nz-card nzTitle="Informations personnelles" class="info-card">
          <div class="info-grid">
            <div class="info-item">
              <label>Email</label>
              <span>
                <a [href]="'mailto:' + member.email" class="contact-link">
                  {{ member.email }}
                </a>
              </span>
            </div>
            <div class="info-item">
              <label>Téléphone</label>
              <span>
                <a [href]="'tel:' + member.phone" class="contact-link">
                  {{ member.phone }}
                </a>
              </span>
            </div>
            <div class="info-item" *ngIf="member.studentCode">
              <label>Code étudiant</label>
              <span class="student-code">{{ member.studentCode }}</span>
            </div>
            <div class="info-item" *ngIf="member.iceNumber">
              <label>ICE Entreprise</label>
              <span class="ice-number">{{ member.iceNumber }}</span>
            </div>
            <div class="info-item">
              <label>Date d'inscription</label>
              <span>{{ getFormattedDate(member.createdAt) }}</span>
            </div>
            <div class="info-item">
              <label>Dernière modification</label>
              <span>{{ member.updatedAt | date:'dd/MM/yyyy HH:mm' }}</span>
            </div>
          </div>
        </nz-card>

        <!-- Abonnement actuel -->
        <nz-card nzTitle="Abonnement actuel" class="info-card">
          <div class="subscription-info" *ngIf="member.subscriptionType; else noSubscription">
            <div class="subscription-item">
              <div class="subscription-details">
                <h4>Type d'abonnement</h4>
                <nz-tag nzColor="blue" style="font-size: 14px; padding: 4px 12px;">
                  {{ member.subscriptionType }}
                </nz-tag>
              </div>
              <div class="subscription-status">
                <nz-tag [nzColor]="getStatusColor(member.status)">
                  {{ getStatusText(member.status) }}
                </nz-tag>
              </div>
            </div>
          </div>
          <ng-template #noSubscription>
            <nz-empty
              nzNotFoundImage="simple"
              nzNotFoundContent="Aucun abonnement actif"
            ></nz-empty>
          </ng-template>
        </nz-card>

        <!-- Historique -->
        <nz-card nzTitle="Historique des activités" class="info-card">
          <nz-spin [nzSpinning]="loadingHistory">
            <div class="history-list" *ngIf="history && history.length > 0; else noHistory">
              <div class="history-item" *ngFor="let item of history">
                <div class="history-icon">
                  <nz-tag [nzColor]="getHistoryTypeColor(item.type)" nzSize="small">
                    <nz-icon nzType="clock-circle" nzTheme="outline"></nz-icon>
                  </nz-tag>
                </div>
                <div class="history-content">
                  <div class="history-action">{{ getHistoryTypeText(item.type) }}</div>
                  <div class="history-description" *ngIf="item.description">
                    {{ item.description }}
                  </div>
                  <div class="history-date">
                    {{ getFormattedDateTime(item.date) }}
                  </div>
                </div>
              </div>
            </div>
            <ng-template #noHistory>
              <nz-empty
                nzNotFoundImage="simple"
                nzNotFoundContent="Aucun historique disponible"
              ></nz-empty>
            </ng-template>
          </nz-spin>
        </nz-card>

        <!-- Historique des paiements -->
        <nz-card class="info-card" nzTitle="Historique des paiements">
          <div class="payments-section">
            <div class="payments-header">
              <div class="payments-stats">
                <div class="stat-item">
                  <span class="stat-value">{{ paymentHistory.length }}</span>
                  <span class="stat-label">Paiements</span>
                </div>
                <div class="stat-item">
                  <span class="stat-value">{{ getTotalPayments() | currency:'MAD ':'symbol':'1.0-0' }}</span>
                  <span class="stat-label">Total payé</span>
                </div>
                <div class="stat-item">
                  <span class="stat-value">{{ getLastPaymentDate() | date:'dd/MM/yyyy' }}</span>
                  <span class="stat-label">Dernier paiement</span>
                </div>
              </div>
            </div>

            <div class="payments-list" *ngIf="paymentHistory.length > 0; else noPayments">
              <div
                *ngFor="let payment of paymentHistory"
                class="payment-item"
                [class.recent-payment]="isRecentPayment(payment)"
              >
                <div class="payment-info">
                  <div class="payment-header">
                    <div class="payment-reference">{{ payment.reference }}</div>
                    <div class="payment-amount">{{ payment.amount | currency:'MAD ':'symbol':'1.2-2' }}</div>
                  </div>
                  <div class="payment-details">
                    <div class="payment-date">
                      <nz-icon nzType="calendar" nzTheme="outline"></nz-icon>
                      {{ payment.date | date:'dd/MM/yyyy à HH:mm' }}
                    </div>
                    <div class="payment-method">
                      <nz-icon nzType="credit-card" nzTheme="outline"></nz-icon>
                      {{ getPaymentMethodLabel(payment.method) }}
                    </div>
                  </div>
                  <div class="payment-notes" *ngIf="payment.notes">
                    {{ payment.notes }}
                  </div>
                </div>
                <div class="payment-actions">
                  <button
                    nz-button
                    nzType="primary"
                    nzSize="small"
                    (click)="downloadPaymentReceipt(payment)"
                    nz-tooltip="Télécharger le reçu PDF"
                  >
                    <nz-icon nzType="download"></nz-icon>
                    PDF
                  </button>
                </div>
              </div>
            </div>

            <ng-template #noPayments>
              <div class="no-payments">
                <nz-icon nzType="file-text" nzTheme="outline"></nz-icon>
                <p>Aucun paiement enregistré</p>
              </div>
            </ng-template>
          </div>
        </nz-card>
      </div>
    </div>
  </div>

  <!-- Template de chargement -->
  <ng-template #loadingTemplate>
    <div class="loading-container">
      <nz-spin nzSize="large" nzTip="Chargement des détails du membre...">
        <div class="loading-content"></div>
      </nz-spin>
    </div>
  </ng-template>
</div>
