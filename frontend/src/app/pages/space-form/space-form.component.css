/* Page container */
.space-form-page {
  padding: 0;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
  background: transparent;
}

/* Header */
.page-header {
  border-bottom: 1px solid #E5E5EA;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 0;
}

.header-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1C1C1E;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title nz-icon {
  color: #6E56CF;
  font-size: 32px;
}

.page-description {
  font-size: 16px;
  color: #8E8E93;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.action-group {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #F2F2F7;
  border-radius: 8px;
}

.action-label {
  font-size: 14px;
  color: #3C3C43;
  font-weight: 500;
}

.header-actions button {
  height: 40px !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
}

/* Form content */
.form-content {
  padding: 0;
  margin-bottom: 24px;
}

/* Form cards */
.form-card {
  margin-bottom: 24px;
  border-radius: 12px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04) !important;
  border: 1px solid #E5E5EA !important;
}

.form-card .ant-card-head {
  border-bottom: 1px solid #E5E5EA !important;
  padding: 20px 24px !important;
  margin-bottom: 0 !important;
}

.form-card .ant-card-head-title {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #1C1C1E !important;
}

.form-card .ant-card-body {
  padding: 24px !important;
  padding-top: 24px !important;
}

/* Card title with action */
.card-title-with-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin: 0;
}

.card-title-with-action span {
  font-size: 18px;
  font-weight: 600;
  color: #1C1C1E;
}

.card-title-with-action button {
  margin-left: 16px;
  height: 32px !important;
  padding: 0 12px !important;
  font-size: 14px !important;
  border-radius: 6px !important;
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
}

.card-title-with-action button nz-icon {
  font-size: 14px !important;
}

/* Equipment container */
div[formArrayName="equipment"] {
  margin-top: 16px;
}

div[formArrayName="rules"] {
  margin-top: 16px;
}

/* Equipment items */
.equipment-item {
  padding: 20px;
  border: 1px solid #E5E5EA;
  border-radius: 8px;
  margin-bottom: 16px;
  background-color: #FAFAFA;
}

.equipment-item:first-child {
  margin-top: 0;
}

.equipment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.equipment-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1C1C1E;
  margin: 0;
}

/* Day schedule */
.day-schedule {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 12px 0;
  border-bottom: 1px solid #F0F0F0;
}

.day-schedule:last-child {
  border-bottom: none;
}

.day-header {
  min-width: 120px;
}

.day-times {
  display: flex;
  gap: 16px;
  flex: 1;
}

.day-times nz-form-item {
  margin-bottom: 0 !important;
}

/* Rule items */
.rule-item {
  margin-bottom: 16px;
}

.rule-item:last-child {
  margin-bottom: 0;
}

/* Empty states */
.empty-state {
  padding: 40px 20px;
  text-align: center;
}

.ant-empty {
  padding: 20px !important;
}

.ant-empty-description {
  color: #8E8E93 !important;
  font-size: 14px !important;
}

/* Form actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px 0;
  border-top: 1px solid #E5E5EA;
  margin-top: 24px;
}

.form-actions button {
  height: 40px !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  padding: 0 24px !important;
}

/* Form items */
.ant-form-item {
  margin-bottom: 20px !important;
}

.ant-form-item-label > label {
  font-weight: 600 !important;
  color: #1C1C1E !important;
}

.ant-form-item-control-input {
  min-height: 40px !important;
}

/* Input styles */
.ant-input,
.ant-select-selector,
.ant-input-number {
  border-radius: 6px !important;
  border-color: #E5E5EA !important;
}

.ant-input:focus,
.ant-select-focused .ant-select-selector,
.ant-input-number-focused {
  border-color: #6E56CF !important;
  box-shadow: 0 0 0 2px rgba(110, 86, 207, 0.1) !important;
}

/* Rules styles */
.rule-item {
  margin-bottom: 16px;
}

.rule-item .ant-input-group-addon {
  background: transparent !important;
  border: none !important;
  padding: 0 8px !important;
}

.rule-item .ant-btn {
  border: none !important;
  box-shadow: none !important;
}

/* Empty state spacing */
.empty-state {
  margin-top: 16px;
}

/* Responsive */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }

  .form-content {
    padding: 20px;
  }

  .day-schedule {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .day-times {
    width: 100%;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions button {
    width: 100%;
  }
}
