import { Component, OnInit, signal, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';

import { NzCardModule } from 'ng-zorro-antd/card';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzMessageModule } from 'ng-zorro-antd/message';

import { FullCalendarModule } from '@fullcalendar/angular';
import { CalendarOptions, EventApi, EventInput } from '@fullcalendar/core';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import frLocale from '@fullcalendar/core/locales/fr';

import { Space } from '../../models/space.model';
import { SpaceService } from '../../services/space.service';
import { Reservation, ReservationFilter } from '../../models/reservation.model';
import { ReservationService } from '../../services/reservation.service';



@Component({
  selector: 'app-reservations',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NzCardModule,
    NzButtonModule,
    NzIconModule,
    NzSelectModule,
    NzMessageModule,
    FullCalendarModule
  ],
  templateUrl: './reservations.component.html',
  styleUrl: './reservations.component.css'
})
export class ReservationsComponent implements OnInit {
  // Signaux pour la réactivité
  private loadingSignal = signal<boolean>(false);
  private spacesSignal = signal<Space[]>([]);
  private reservationsSignal = signal<Reservation[]>([]);

  // Getters pour les templates
  get loading() { return this.loadingSignal(); }
  get spaces() { return this.spacesSignal(); }
  get reservations() { return this.reservationsSignal(); }

  // Filtres
  selectedSpaceId: string | null = null;
  selectedStatus: string | null = null;

  // FullCalendar options
  calendarOptions: CalendarOptions = {
    plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin],
    headerToolbar: {
      left: 'prev,next today',
      center: 'title',
      right: 'dayGridMonth,timeGridWeek,timeGridDay'
    },
    initialView: 'dayGridMonth',
    locale: frLocale,
    weekends: true,
    editable: true,
    selectable: true,
    selectMirror: true,
    dayMaxEvents: true,
    height: 'auto',
    businessHours: {
      daysOfWeek: [1, 2, 3, 4, 5, 6], // Lundi à samedi
      startTime: '08:00',
      endTime: '20:00'
    },
    slotMinTime: '08:00:00',
    slotMaxTime: '20:00:00',
    allDaySlot: false,
    events: [],
    eventClick: this.handleEventClick.bind(this),
    select: this.handleDateSelect.bind(this),
    eventDrop: this.handleEventDrop.bind(this),
    eventResize: this.handleEventResize.bind(this)
  };

  constructor(
    private spaceService: SpaceService,
    private message: NzMessageService,
    private router: Router,
    private reservationService: ReservationService
  ) {}

  ngOnInit() {
    this.loadSpaces();
    this.loadReservations();
  }

  private loadSpaces() {
    this.loadingSignal.set(true);
    this.spaceService.getSpaces().subscribe({
      next: (spaces) => {
        this.spacesSignal.set(spaces);
        this.loadingSignal.set(false);
      },
      error: (error) => {
        console.error('Erreur lors du chargement des espaces:', error);
        this.message.error('Erreur lors du chargement des espaces');
        this.loadingSignal.set(false);
      }
    });
  }

  private loadReservations() {
    this.loadingSignal.set(true);

    // Construire les filtres basés sur les filtres actuels
    const filters: ReservationFilter = {};

    if (this.selectedSpaceId) {
      filters.spaceId = this.selectedSpaceId;
    }

    if (this.selectedStatus) {
      filters.status = this.selectedStatus as any; // Cast temporaire pour la compatibilité
    }

    this.reservationService.getReservations(filters).subscribe({
      next: (reservations) => {
        this.reservationsSignal.set(reservations);
        this.updateCalendarEvents();
        this.loadingSignal.set(false);
      },
      error: (error) => {
        console.error('Error loading reservations:', error);
        this.message.error('Erreur lors du chargement des réservations');
        this.loadingSignal.set(false);
      }
    });
  }



  private updateCalendarEvents() {
    const events: EventInput[] = this.reservations
      .filter(reservation => this.matchesFilters(reservation))
      .map(reservation => ({
        id: reservation.id,
        title: `${reservation.spaceName} - ${reservation.userName}`,
        start: reservation.startTime,
        end: reservation.endTime,
        backgroundColor: this.getEventColor(reservation.status),
        borderColor: this.getEventColor(reservation.status),
        textColor: '#ffffff',
        className: `${reservation.status}-event`,
        extendedProps: {
          reservation: reservation,
          status: reservation.status,
          spaceName: reservation.spaceName,
          userName: reservation.userName,
          purpose: reservation.purpose
        }
      }));

    this.calendarOptions = {
      ...this.calendarOptions,
      events: events
    };
  }

  private getEventColor(status: string): string {
    switch (status) {
      case 'confirmed': return '#6E56CF'; // Violet principal pour confirmé
      case 'pending': return '#B8A9E8';   // Violet soft pour en attente
      case 'cancelled': return '#C7C7CC'; // Gris plus clair pour annulé
      default: return '#1890ff';          // Bleu par défaut
    }
  }

  // Méthodes de filtrage
  onSpaceFilterChange(spaceId: string | null) {
    this.selectedSpaceId = spaceId;
    this.loadReservations(); // Recharger les données avec le nouveau filtre
  }

  onStatusFilterChange(status: string | null) {
    this.selectedStatus = status;
    this.loadReservations(); // Recharger les données avec le nouveau filtre
  }

  // Handlers FullCalendar
  handleEventClick(clickInfo: any) {
    const reservation = clickInfo.event.extendedProps['reservation'];
    this.viewReservationDetails(reservation);
  }

  handleDateSelect(selectInfo: any) {
    // Naviguer vers le formulaire de réservation avec la date sélectionnée
    this.router.navigate(['/reservation-form'], {
      queryParams: {
        date: selectInfo.startStr,
        spaceId: this.selectedSpaceId
      }
    });
  }

  handleEventDrop(dropInfo: any) {
    const reservation = dropInfo.event.extendedProps['reservation'];
    // Ici on pourrait mettre à jour la réservation avec les nouvelles dates
    console.log('Réservation déplacée:', reservation, dropInfo.event.start, dropInfo.event.end);
    this.message.success('Réservation mise à jour');
  }

  handleEventResize(resizeInfo: any) {
    const reservation = resizeInfo.event.extendedProps['reservation'];
    // Ici on pourrait mettre à jour la durée de la réservation
    console.log('Réservation redimensionnée:', reservation, resizeInfo.event.start, resizeInfo.event.end);
    this.message.success('Durée de réservation mise à jour');
  }

  private matchesFilters(reservation: Reservation): boolean {
    if (this.selectedSpaceId && reservation.spaceId !== this.selectedSpaceId) {
      return false;
    }
    if (this.selectedStatus && reservation.status !== this.selectedStatus) {
      return false;
    }
    return true;
  }

  // Méthodes de formatage
  formatTime(date: Date): string {
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'confirmed': return 'green';
      case 'pending': return 'orange';
      case 'cancelled': return 'red';
      default: return 'default';
    }
  }

  getStatusLabel(status: string): string {
    switch (status) {
      case 'confirmed': return 'Confirmée';
      case 'pending': return 'En attente';
      case 'cancelled': return 'Annulée';
      default: return status;
    }
  }

  // Méthodes d'actions
  navigateToReservationForm() {
    this.router.navigate(['/reservation-form']);
  }

  editReservation(reservation: Reservation) {
    this.router.navigate(['/reservation-form'], {
      queryParams: {
        id: reservation.id,
        date: reservation.startTime.toISOString(),
        spaceId: reservation.spaceId
      }
    });
  }

  viewReservation(reservation: Reservation) {
    this.router.navigate(['/reservations', reservation.id]);
  }

  viewReservationDetails(reservation: Reservation) {
    this.router.navigate(['/reservations', reservation.id]);
  }

  cancelReservation(reservation: Reservation) {
    // Logique d'annulation
    console.log('Annuler réservation:', reservation);
    this.message.success('Réservation annulée');
  }

}
