import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { firstValueFrom } from 'rxjs';

import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzSpinModule } from 'ng-zorro-antd/spin';

import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-tenant-selection',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzCardModule,
    NzIconModule,
    NzSpinModule
  ],
  template: `
    <div class="tenant-selection-container">
      <!-- Logo centré à l'extérieur de la card -->
      <div class="logo-section">
        <img src="assets/images/logo.png" alt="Workeem" class="logo">
      </div>

      <!-- Card principale -->
      <div class="login-card">
        <div class="card-header">
          <h1>Bienvenue sur Workeem</h1>
          <p class="subtitle">Entrez le nom de votre espace de gestion</p>
        </div>

        <form [formGroup]="tenantForm" (ngSubmit)="onSubmit()" class="login-form">
          <!-- Champ nom du coworking -->
          <div class="form-group">
            <label class="form-label">Nom de votre espace de coworking</label>
            <div class="input-wrapper">
              <input
                type="text"
                formControlName="tenantId"
                placeholder="Ex: workeem-paris, mon-coworking..."
                [disabled]="loading"
                class="form-input"
              />
            </div>
            <div class="form-help">
              Entrez le nom de votre espace tel que fourni par votre administrateur
            </div>
          </div>

          <!-- Bouton continuer -->
          <button
            type="submit"
            [disabled]="!tenantForm.valid || loading"
            class="submit-button"
            [class.loading]="loading"
          >
            <span *ngIf="!loading">
              Continuer
              <nz-icon nzType="arrow-right" nzTheme="outline"></nz-icon>
            </span>
            <nz-spin *ngIf="loading" nzSimple></nz-spin>
          </button>
        </form>

        <!-- Footer -->
        <div class="card-footer">
          <p>© 2025 Workeem. Tous droits réservés.</p>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .tenant-selection-container {
      min-height: 100vh;
      width: 100vw;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #e8e5ff 0%, #d4c5ff 50%, #c8b5ff 100%);
      padding: 40px 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      position: fixed;
      top: 0;
      left: 0;
      z-index: 1000;
    }

    .logo-section {
      text-align: center;
      margin-bottom: 40px;
    }

    .logo {
      height: 80px;
      width: auto;
    }

    .login-card {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      padding: 40px;
      width: 100%;
      max-width: 400px;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .card-header {
      text-align: center;
      margin-bottom: 32px;
    }

    .card-header h1 {
      color: #333;
      font-size: 18px;
      font-weight: 500;
      margin: 0 0 8px 0;
      line-height: 1.4;
    }

    .subtitle {
      color: #666;
      font-size: 14px;
      margin: 0;
      font-weight: 400;
    }

    .login-form {
      margin-bottom: 32px;
    }

    .form-group {
      margin-bottom: 24px;
    }

    .form-label {
      display: block;
      color: #374151;
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 8px;
    }

    .form-help {
      color: #6b7280;
      font-size: 12px;
      margin-top: 6px;
      line-height: 1.4;
    }

    .input-wrapper {
      position: relative;
    }

    .form-input {
      width: 100%;
      height: 50px;
      padding: 0 16px;
      border: 1px solid #e1e5e9;
      border-radius: 12px;
      font-size: 16px;
      background: #f8f9fa;
      transition: all 0.2s ease;
      box-sizing: border-box;
    }

    .form-input:focus {
      outline: none;
      border-color: #7c3aed;
      background: white;
      box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
    }

    .form-input:disabled {
      background: #f5f5f5;
      color: #999;
      cursor: not-allowed;
    }

    .submit-button {
      width: 100%;
      height: 50px;
      background: #7c3aed;
      color: white;
      border: none;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .submit-button:hover:not(:disabled) {
      background: #6d28d9;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3);
    }

    .submit-button:active:not(:disabled) {
      transform: translateY(0);
    }

    .submit-button:disabled {
      background: #d1d5db;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .submit-button.loading {
      background: #7c3aed;
    }

    .submit-button nz-icon {
      margin-left: 8px;
      font-size: 14px;
    }

    .card-footer {
      text-align: center;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;
    }

    .card-footer p {
      color: #9ca3af;
      font-size: 12px;
      margin: 0;
    }

    @media (max-width: 480px) {
      .tenant-selection-container {
        padding: 20px 16px;
      }

      .logo {
        height: 60px;
      }

      .login-card {
        padding: 32px 24px;
      }

      .card-header h1 {
        font-size: 16px;
      }
    }
  `]
})
export class TenantSelectionComponent {
  tenantForm: FormGroup;
  loading = false;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private http: HttpClient,
    private message: NzMessageService,
    private authService: AuthService
  ) {
    this.tenantForm = this.fb.group({
      tenantId: ['', [Validators.required, Validators.minLength(2)]]
    });
  }

  async onSubmit() {
    if (this.tenantForm.valid) {
      this.loading = true;
      const tenantId = this.tenantForm.value.tenantId.toLowerCase().trim();

      try {
        // Vérifier si le tenant existe
        const response = await firstValueFrom(
          this.http.get<{exists: boolean, tenantId: string}>(`http://localhost:8080/api/tenants/verify/${tenantId}`)
        );

        if (response.exists) {
          // Stocker le tenantId dans le localStorage
          localStorage.setItem('workeem_tenant_id', tenantId);
          console.log('🏢 Tenant ID stored:', tenantId);

          // Vérifier si l'utilisateur est déjà connecté
          if (this.authService.isAuthenticated()) {
            // Si déjà connecté, rediriger vers l'URL stockée ou welcome
            const redirectUrl = localStorage.getItem('workeem_redirect_url') || '/welcome';
            localStorage.removeItem('workeem_redirect_url');
            console.log('🔄 Already authenticated, redirecting to:', redirectUrl);
            this.router.navigateByUrl(redirectUrl);
          } else {
            // Sinon, rediriger vers Keycloak pour l'authentification
            console.log('🔑 Redirecting to Keycloak login');
            this.authService.login();
          }
        } else {
          console.log('❌ Tenant not found:', tenantId);
          this.message.error('Espace de coworking non trouvé. Vérifiez le nom saisi.');
          this.loading = false;
        }
      } catch (error) {
        console.error('Erreur lors de la vérification du tenant:', error);
        this.message.error('Erreur de connexion. Veuillez réessayer.');
        this.loading = false;
      }
    }
  }
}
