.members-container {
  padding: 0;
  max-width: 1400px;
  margin: 0 auto;
}

/* En-tête de page */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 24px 0;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: #1C1C1E;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title .anticon {
  color: #6E56CF;
  font-size: 32px;
}

.page-subtitle {
  font-size: 16px;
  color: #8E8E93;
  margin: 0;
  font-weight: 400;
}

.add-button {
  height: 48px !important;
  border-radius: 12px !important;
  font-weight: 500 !important;
  font-size: 16px !important;
  padding: 0 24px !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  background-color: #6E56CF !important;
  border-color: #6E56CF !important;
}

.add-button:hover {
  background-color: #5A47B8 !important;
  border-color: #5A47B8 !important;
  transform: translateY(-1px);
}

/* Carte des filtres */
.filters-card {
  margin-bottom: 24px;
  border-radius: 12px !important;
  border: 1px solid #E5E5EA !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
}

.filters-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr auto;
  gap: 24px;
  align-items: end;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-item label {
  font-weight: 500;
  color: #1C1C1E;
  font-size: 14px;
}

/* Correction du double border pour le champ de recherche */
.filter-item .ant-input-group {
  border-radius: 8px;
}

.filter-item .ant-input-group .ant-input {
  border: none !important;
  box-shadow: none !important;
}

.filter-item .ant-input-group .ant-input:focus {
  border: none !important;
  box-shadow: none !important;
}

/* Bouton Clear Filters */
.clear-filters-btn {
  border-radius: 8px !important;
  border: 1px solid #D1D1D6 !important;
  color: #8E8E93 !important;
  background-color: #FFFFFF !important;
  height: 32px !important;
  padding: 0 12px !important;
  font-size: 14px !important;
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
}

.clear-filters-btn:hover {
  border-color: #6E56CF !important;
  color: #6E56CF !important;
  background-color: #F8F9FA !important;
}

/* Carte du tableau */
.table-card {
  border-radius: 12px !important;
  border: 1px solid #E5E5EA !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  overflow: hidden;
}

/* Container responsive pour le tableau */
.table-card .ant-card-body {
  padding: 0 !important;
}

.table-wrapper {
  overflow-x: auto !important;
  -webkit-overflow-scrolling: touch;
  width: 100%;
}

/* Forcer le scroll horizontal */
.ant-table-container {
  overflow-x: auto !important;
}

/* Colonne sticky */
.ant-table-thead > tr > th[nz-left],
.ant-table-tbody > tr > td[nz-left] {
  position: sticky !important;
  left: 0 !important;
  z-index: 10 !important;
  background: #fff !important;
  box-shadow: 2px 0 4px rgba(0,0,0,0.1) !important;
}

.ant-table-thead > tr > th[nz-left] {
  background: #F2F2F7 !important;
}

/* Styles du tableau */
.ant-table {
  border-radius: 8px !important;
}

.ant-table-container {
  overflow-x: auto !important;
  -webkit-overflow-scrolling: touch;
}

.ant-table-thead > tr > th {
  background-color: #F2F2F7 !important;
  border-bottom: 1px solid #E5E5EA !important;
  font-weight: 600 !important;
  color: #1C1C1E !important;
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Contenu des cellules */
.ant-table-tbody > tr > td {
  white-space: normal !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  vertical-align: top !important;
}

.ant-table-tbody > tr:hover > td {
  background-color: #EDE9F8 !important;
}

/* Informations du membre */
.member-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #6E56CF;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.member-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.member-name {
  font-weight: 500;
  color: #1C1C1E;
  font-size: 14px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  line-height: 1.3;
}

.member-id {
  font-size: 12px;
  color: #8E8E93;
}

/* Informations de contact */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.email {
  font-size: 14px;
  color: #1C1C1E;
  word-wrap: break-word;
  overflow-wrap: break-word;
  line-height: 1.3;
}

.phone {
  font-size: 12px;
  color: #8E8E93;
}

/* Code étudiant */
.student-code {
  font-family: 'Courier New', monospace;
  background-color: #EDE9F8;
  color: #6E56CF;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.ice-number {
  font-family: 'Courier New', monospace;
  background-color: #F3E8FF;
  color: #8B5CF6;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.no-code {
  color: #8E8E93;
  font-style: italic;
}

/* Menu d'actions dropdown */
.action-dropdown {
  display: flex;
  justify-content: center;
}

.action-trigger {
  width: 32px !important;
  height: 32px !important;
  border-radius: 6px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: #8E8E93 !important;
  transition: all 0.2s ease;
}

.action-trigger:hover {
  color: #6E56CF !important;
  background-color: #EDE9F8 !important;
  transform: translateY(-1px);
}

/* Styles pour les items du menu dropdown */
.ant-dropdown-menu {
  border-radius: 8px !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid #E5E5EA !important;
  padding: 4px !important;
  min-width: 160px !important;
}

.ant-dropdown-menu .ant-dropdown-menu-item {
  border-radius: 6px !important;
  margin: 2px !important;
  padding: 12px 16px !important;
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 12px !important;
  font-size: 14px !important;
  color: #1C1C1E !important;
  white-space: nowrap !important;
  min-width: 150px !important;
  width: auto !important;
  box-sizing: border-box !important;
  height: auto !important;
  line-height: normal !important;
}

.ant-dropdown-menu .ant-dropdown-menu-item .anticon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
  display: inline-block !important;
  flex-shrink: 0 !important;
  vertical-align: middle !important;
}

.ant-dropdown-menu .ant-dropdown-menu-item span {
  display: inline-block !important;
  white-space: nowrap !important;
  vertical-align: middle !important;
  line-height: normal !important;
  margin-left: 4px !important;
}

.ant-dropdown-menu .ant-dropdown-menu-item:hover {
  background-color: #EDE9F8 !important;
  color: #6E56CF !important;
}

.ant-dropdown-menu .ant-dropdown-menu-item.danger-item {
  color: #FF3B30 !important;
}

.ant-dropdown-menu .ant-dropdown-menu-item.danger-item:hover {
  background-color: #FFE5E5 !important;
  color: #FF3B30 !important;
}

.ant-dropdown-menu .ant-dropdown-menu-divider {
  margin: 4px 0 !important;
  background-color: #E5E5EA !important;
}

/* Tags personnalisés */
.ant-tag {
  border-radius: 6px !important;
  font-weight: 500 !important;
  font-size: 12px !important;
  padding: 2px 8px !important;
  border: none !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  white-space: normal !important;
  line-height: 1.2 !important;
}

/* Modales */
.ant-modal-header {
  border-radius: 12px 12px 0 0 !important;
  border-bottom: 1px solid #E5E5EA !important;
}

.ant-modal-content {
  border-radius: 12px !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
}

.ant-modal-title {
  font-weight: 600 !important;
  color: #1C1C1E !important;
}

/* Formulaires */
.ant-form-item-label > label {
  font-weight: 500 !important;
  color: #1C1C1E !important;
}

.ant-input,
.ant-select-selector {
  border-radius: 8px !important;
  border-color: #E5E5EA !important;
}

.ant-input:focus,
.ant-select-focused .ant-select-selector {
  border-color: #6E56CF !important;
  box-shadow: 0 0 0 2px rgba(110, 86, 207, 0.1) !important;
}

/* Responsive */
@media (max-width: 1200px) {
  .filters-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }


}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .add-button {
    width: 100%;
    justify-content: center !important;
  }

  .members-container {
    padding: 0 8px;
  }

  /* Tableau responsive pour mobile */
  .ant-table {
    font-size: 12px;
  }

  .member-info {
    gap: 8px;
  }

  .member-avatar {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }

  /* Améliorer l'affichage des informations de contact sur mobile */
  .contact-info .email {
    font-size: 11px;
  }

  .contact-info .phone {
    font-size: 10px;
  }

  /* Tags plus petits sur mobile */
  .ant-tag {
    font-size: 10px !important;
    padding: 1px 6px !important;
  }
}

/* États de chargement */
.ant-spin-container {
  min-height: 400px;
}

/* Pagination */
.ant-pagination {
  margin-top: 16px !important;
  text-align: center;
}

.ant-pagination-item-active {
  background-color: #6E56CF !important;
  border-color: #6E56CF !important;
}

.ant-pagination-item-active a {
  color: white !important;
}

/* Messages d'état vide */
.ant-empty {
  padding: 40px 0 !important;
}

.ant-empty-description {
  color: #8E8E93 !important;
}
