import { Component, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';

import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzTimePickerModule } from 'ng-zorro-antd/time-picker';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzAlertModule } from 'ng-zorro-antd/alert';
import { NzMessageModule } from 'ng-zorro-antd/message';

import {EquipmentStatus, Space} from '../../models/space.model';
import { SpaceService } from '../../services/space.service';
import { Reservation } from '../../models/reservation.model';
import { ReservationService } from '../../services/reservation.service';
import { MemberService } from '../../services/member.service';
import { Member, getMemberFullName, MemberType } from '../../models/member.model';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-reservation-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    NzSelectModule,
    NzDatePickerModule,
    NzTimePickerModule,
    NzButtonModule,
    NzCardModule,
    NzIconModule,
    NzTagModule,
    NzGridModule,
    NzRadioModule,
    NzInputNumberModule,
    NzAlertModule,
    NzMessageModule
  ],
  templateUrl: './reservation-form.component.html',
  styleUrl: './reservation-form.component.css'
})
export class ReservationFormComponent implements OnInit {

  // Signaux pour la réactivité
  private loadingSignal = signal<boolean>(false);
  private savingSignal = signal<boolean>(false);
  private spacesSignal = signal<Space[]>([]);
  private membersSignal = signal<Member[]>([]);

  // Getters pour les templates
  get loading() { return this.loadingSignal(); }
  get saving() { return this.savingSignal(); }
  get spaces() { return this.spacesSignal(); }
  get members() { return this.membersSignal(); }

  // Formulaire
  reservationForm!: FormGroup;
  isEditMode = false;
  selectedSpace: Space | null = null;
  editingReservationId: string | null = null;
  editingReservation: Reservation | null = null;

  // Enums pour les templates
  MemberType = MemberType;

  // Options pour les listes déroulantes
  statusOptions = [
    { value: 'confirmed', label: 'Confirmée' },
    { value: 'pending', label: 'En attente' },
    { value: 'cancelled', label: 'Annulée' }
  ];

  recurrenceOptions = [
    { value: 'none', label: 'Aucune récurrence' },
    { value: 'daily', label: 'Quotidienne' },
    { value: 'weekly', label: 'Hebdomadaire' },
    { value: 'monthly', label: 'Mensuelle' }
  ];

  constructor(
    private fb: FormBuilder,
    private spaceService: SpaceService,
    private router: Router,
    private route: ActivatedRoute,
    private reservationService: ReservationService,
    private memberService: MemberService,
    private message: NzMessageService
  ) {
    this.initializeForm();
  }

  ngOnInit() {
    this.loadSpaces();
    this.loadMembers();
    this.handleQueryParams();
  }

  private handleQueryParams() {
    this.route.queryParams.subscribe(params => {
      if (params['id']) {
        this.isEditMode = true;
        this.editingReservationId = params['id'];
        this.loadReservation(params['id']);
      }

      if (params['date']) {
        const date = new Date(params['date']);
        this.reservationForm.patchValue({
          date: date
        });
      }

      if (params['spaceId']) {
        this.reservationForm.patchValue({
          spaceId: params['spaceId']
        });
        this.onSpaceChange(params['spaceId']);
      }

      // Traitement des nouveaux paramètres startTime et endTime
      if (params['startTime'] && params['endTime']) {
        // Créer les dates en heure locale pour éviter les problèmes de timezone
        const startTime = new Date(params['startTime']);
        const endTime = new Date(params['endTime']);

        console.log('Paramètres reçus:', {
          startTimeParam: params['startTime'],
          endTimeParam: params['endTime'],
          startTimeLocal: startTime.toString(),
          endTimeLocal: endTime.toString()
        });

        // Extraire la date en heure locale
        const reservationDate = new Date(startTime.getFullYear(), startTime.getMonth(), startTime.getDate());

        // Extraire les heures en heure locale
        const startHour = startTime.getHours();
        const startMinute = startTime.getMinutes();
        const endHour = endTime.getHours();
        const endMinute = endTime.getMinutes();

        console.log('Heures extraites:', {
          startHour,
          startMinute,
          endHour,
          endMinute
        });

        // Créer les objets Date pour les time pickers (aujourd'hui avec les bonnes heures)
        const today = new Date();
        const startTimeForPicker = new Date(today.getFullYear(), today.getMonth(), today.getDate(), startHour, startMinute);
        const endTimeForPicker = new Date(today.getFullYear(), today.getMonth(), today.getDate(), endHour, endMinute);

        // Calculer la durée en heures et minutes
        const durationInMinutes = (endTime.getTime() - startTime.getTime()) / (1000 * 60);
        const durationHours = Math.floor(durationInMinutes / 60);
        const durationMinutesRemainder = durationInMinutes % 60;

        console.log('Valeurs pour le formulaire:', {
          date: reservationDate,
          startTime: startTimeForPicker,
          endTime: endTimeForPicker,
          durationHours: durationHours,
          durationMinutes: durationMinutesRemainder
        });

        // Mettre à jour le formulaire
        this.reservationForm.patchValue({
          date: reservationDate,
          startTime: startTimeForPicker,
          endTime: endTimeForPicker,
          durationHours: durationHours,
          durationMinutes: durationMinutesRemainder
        });
      }
    });
  }



  private loadMembers() {
    this.memberService.getMembers().subscribe({
      next: (members: Member[]) => {
        this.membersSignal.set(members);
      },
      error: (error: any) => {
        console.error('Error loading members:', error);
        this.message.error('Erreur lors du chargement des members');
      }
    });
  }

  private updateMemberValidations(memberType: string) {
    const existingMemberControl = this.reservationForm.get('existingMemberId');
    const firstNameControl = this.reservationForm.get('firstName');
    const lastNameControl = this.reservationForm.get('lastName');
    const emailControl = this.reservationForm.get('email');
    const phoneControl = this.reservationForm.get('phone');

    if (memberType === 'existing') {
      // Member existant : seul l'ID est requis
      existingMemberControl?.setValidators([Validators.required]);
      firstNameControl?.clearValidators();
      lastNameControl?.clearValidators();
      emailControl?.clearValidators();
      phoneControl?.clearValidators();
    } else {
      // Nouveau member : nom, prénom et téléphone requis, email optionnel
      existingMemberControl?.clearValidators();
      firstNameControl?.setValidators([Validators.required]);
      lastNameControl?.setValidators([Validators.required]);
      emailControl?.setValidators([Validators.email]); // Email optionnel mais doit être valide si renseigné
      phoneControl?.setValidators([Validators.required]);
    }

    // Mettre à jour les validations
    existingMemberControl?.updateValueAndValidity();
    firstNameControl?.updateValueAndValidity();
    lastNameControl?.updateValueAndValidity();
    emailControl?.updateValueAndValidity();
    phoneControl?.updateValueAndValidity();
  }

  private loadReservation(id: string) {
    // Simulation de chargement de réservation
    // En réalité, on ferait un appel au service
    const mockReservations: Reservation[] = [
      {
        id: '1',
        spaceId: '1',
        spaceName: 'Salle de réunion Alpha',
        userId: '1',
        userName: 'Jean Dupont',
        userEmail: '<EMAIL>',
        startTime: new Date(2024, 0, 15, 9, 0),
        endTime: new Date(2024, 0, 15, 11, 0),
        purpose: 'Réunion équipe marketing',
        status: 'confirmed',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    this.editingReservation = mockReservations.find(r => r.id === id) || null;
    if (this.editingReservation) {
      this.populateForm(this.editingReservation);
    }
  }

  private initializeForm() {
    const defaultStartTime = new Date();
    defaultStartTime.setHours(9, 0, 0, 0); // 9h00 par défaut

    const defaultEndTime = new Date();
    defaultEndTime.setHours(10, 0, 0, 0); // 10h00 par défaut

    this.reservationForm = this.fb.group({
      spaceId: ['', [Validators.required]],
      startDate: [new Date(), [Validators.required]],
      endDate: [new Date(), [Validators.required]],
      durationMonths: [0, [Validators.required, Validators.min(0)]],
      durationDays: [0, [Validators.required, Validators.min(0)]],
      durationHours: [1, [Validators.required, Validators.min(0), Validators.max(23)]],
      durationMinutes: [0, [Validators.required]],
      numberOfPeople: [1, [Validators.required, Validators.min(1)]],
      purpose: [''], // Rendu optionnel
      status: ['confirmed', [Validators.required]], // Statut de la réservation
      recurrence: ['none'],
      notes: [''],
      // Nouveaux champs pour le member
      memberType: ['existing', [Validators.required]], // 'existing' ou 'new'
      existingMemberId: [null], // Pas de valeur par défaut
      // Champs pour nouveau member
      firstName: [''],
      lastName: [''],
      email: [''],
      phone: [''],
      company: [''],
      newMemberType: [''], // Type du nouveau membre (STUDENT, PROFESSIONAL, COMPANY)
      studentCode: [''], // Code étudiant (si type STUDENT)
      iceNumber: [''], // Numéro ICE (si type COMPANY)
      subscriptionType: ['']
    });

    // Validation conditionnelle pour les champs member
    this.setupMemberValidation();

    // Gérer les changements de dates pour recalculer la durée
    this.reservationForm.get('startDate')?.valueChanges.subscribe(() => {
      this.calculateDurationFromDates();
    });

    this.reservationForm.get('endDate')?.valueChanges.subscribe(() => {
      this.calculateDurationFromDates();
    });



    // Gérer les validations conditionnelles pour le member
    this.reservationForm.get('memberType')?.valueChanges.subscribe((type) => {
      this.updateMemberValidations(type);
    });

    // Calculer la durée initiale
    setTimeout(() => {
      this.calculateDurationFromDates();
    }, 100);
  }

  private loadSpaces() {
    this.loadingSignal.set(true);
    this.spaceService.getSpaces().subscribe({
      next: (spaces) => {
        this.spacesSignal.set(spaces);
        this.loadingSignal.set(false);
      },
      error: (error) => {
        console.error('Erreur lors du chargement des espaces:', error);
        this.loadingSignal.set(false);
      }
    });
  }

  private populateForm(reservation: Reservation) {
    this.reservationForm.patchValue({
      spaceId: reservation.spaceId,
      date: new Date(reservation.startTime),
      startTime: new Date(reservation.startTime),
      endTime: new Date(reservation.endTime),
      durationHours: Math.floor(this.calculateDuration(reservation.startTime, reservation.endTime) / 60),
      durationMinutes: this.calculateDuration(reservation.startTime, reservation.endTime) % 60,
      purpose: reservation.purpose || '',
      notes: '',
      recurrence: 'none'
    });

    this.onSpaceChange(reservation.spaceId);
  }

  private calculateDuration(startTime: Date, endTime: Date): number {
    return Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60));
  }

  // Méthodes d'événements du formulaire
  onSpaceChange(spaceId: string) {
    this.selectedSpace = this.spaces.find(space => space.id === spaceId) || null;
  }

  calculateDurationFromDates() {
    const startDate = this.reservationForm.get('startDate')?.value;
    const endDate = this.reservationForm.get('endDate')?.value;
    const startTime = this.reservationForm.get('startTime')?.value;
    const endTime = this.reservationForm.get('endTime')?.value;

    if (!startDate || !endDate || !startTime || !endTime) {
      return;
    }

    // Si la date de fin est antérieure à la date de début, l'ajuster
    const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
    const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());

    if (endDateOnly < startDateOnly) {
      this.reservationForm.patchValue({ endDate: startDate }, { emitEvent: false });
      return;
    }

    // Créer les dates complètes avec heures
    const startDateTime = new Date(startDate);
    startDateTime.setHours(startTime.getHours(), startTime.getMinutes(), 0, 0);

    const endDateTime = new Date(endDate);
    endDateTime.setHours(endTime.getHours(), endTime.getMinutes(), 0, 0);

    // Si l'heure de fin est antérieure à l'heure de début le même jour, ajuster
    if (startDateOnly.getTime() === endDateOnly.getTime() && endDateTime <= startDateTime) {
      endDateTime.setHours(startDateTime.getHours() + 1, startDateTime.getMinutes(), 0, 0);
      this.reservationForm.patchValue({
        endTime: endDateTime
      }, { emitEvent: false });
    }

    // Calculer la différence
    const diffMs = endDateTime.getTime() - startDateTime.getTime();

    if (diffMs <= 0) {
      return;
    }

    // Calculer mois, jours, heures, minutes
    const diffMonths = Math.floor(diffMs / (1000 * 60 * 60 * 24 * 30)); // Approximation
    const remainingAfterMonths = diffMs % (1000 * 60 * 60 * 24 * 30);

    const diffDays = Math.floor(remainingAfterMonths / (1000 * 60 * 60 * 24));
    const remainingAfterDays = remainingAfterMonths % (1000 * 60 * 60 * 24);

    const diffHours = Math.floor(remainingAfterDays / (1000 * 60 * 60));
    const diffMinutes = Math.floor((remainingAfterDays % (1000 * 60 * 60)) / (1000 * 60));

    // Mettre à jour les champs de durée
    this.reservationForm.patchValue({
      durationMonths: diffMonths,
      durationDays: diffDays,
      durationHours: diffHours,
      durationMinutes: diffMinutes
    }, { emitEvent: false });
  }

  getDurationText(): string {
    const startDate = this.reservationForm.get('startDate')?.value;
    const endDate = this.reservationForm.get('endDate')?.value;

    if (!startDate || !endDate) {
      return '';
    }

    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return 'Réservation sur la même journée';
    } else if (diffDays === 1) {
      return 'Réservation sur 1 jour';
    } else {
      return `Réservation sur ${diffDays} jours`;
    }
  }

  disabledEndDate = (endDate: Date): boolean => {
    const startDate = this.reservationForm.get('startDate')?.value;
    if (!startDate) {
      return false;
    }
    // Permettre la même date ou une date ultérieure
    const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
    const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());
    return endDateOnly < startDateOnly;
  };

  onEndTimeChange() {
    this.calculateDurationFromDates();
  }

  getTotalDurationText(): string {
    const months = this.reservationForm.get('durationMonths')?.value || 0;
    const days = this.reservationForm.get('durationDays')?.value || 0;
    const hours = this.reservationForm.get('durationHours')?.value || 0;
    const minutes = this.reservationForm.get('durationMinutes')?.value || 0;

    if (months === 0 && days === 0 && hours === 0 && minutes === 0) {
      return '';
    }

    let text = '';
    if (months > 0) {
      text += `${months} mois`;
    }
    if (days > 0) {
      if (text) text += ', ';
      text += `${days} jour${days > 1 ? 's' : ''}`;
    }
    if (hours > 0) {
      if (text) text += ', ';
      text += `${hours}h`;
    }
    if (minutes > 0) {
      if (text) text += ', ';
      text += `${minutes}min`;
    }

    return text;
  }

  onStartTimeChange(time: Date) {
    this.calculateDurationFromDates();
  }

  // Méthodes de validation
  disabledDate = (current: Date): boolean => {
    // Désactiver les dates passées
    return current && current < new Date(new Date().setHours(0, 0, 0, 0));
  };

  disabledHours = (): number[] => {
    const disabled: number[] = [];
    const selectedDate = this.reservationForm.get('date')?.value;

    if (selectedDate && this.isSameDay(selectedDate, new Date())) {
      // Si c'est aujourd'hui, désactiver les heures passées
      const currentHour = new Date().getHours();
      for (let i = 0; i < currentHour; i++) {
        disabled.push(i);
      }
    }

    // Désactiver les heures en dehors des heures d'ouverture (8h-20h)
    for (let i = 0; i < 8; i++) {
      disabled.push(i);
    }
    for (let i = 20; i < 24; i++) {
      disabled.push(i);
    }

    return disabled;
  };

  disabledMinutes = (selectedHour: number): number[] => {
    const disabled: number[] = [];
    const selectedDate = this.reservationForm.get('date')?.value;

    if (selectedDate && this.isSameDay(selectedDate, new Date())) {
      const currentHour = new Date().getHours();
      const currentMinute = new Date().getMinutes();

      if (selectedHour === currentHour) {
        // Désactiver les minutes passées pour l'heure actuelle
        for (let i = 0; i <= currentMinute; i++) {
          disabled.push(i);
        }
      }
    }

    // Permettre seulement les créneaux de 15 minutes
    return [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59];
  };

  private isSameDay(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }

  // Méthodes d'actions
  onSubmit() {
    if (this.reservationForm.valid) {
      this.savingSignal.set(true);

      try {
        const formValue = this.reservationForm.value;

        // Validation des champs requis
        if (!formValue.startDate || !formValue.endDate) {
          throw new Error('Date et heure de début et de fin sont requises');
        }

        const startDateTime = new Date(formValue.startDate);
        const endDateTime = new Date(formValue.endDate);

        // Validation que l'heure de fin est après l'heure de début
        if (endDateTime <= startDateTime) {
          throw new Error('L\'heure de fin doit être après l\'heure de début');
        }

      // Préparer les données pour l'API backend
      const reservationData: any = {
        spaceId: formValue.spaceId,
        spaceName: this.selectedSpace?.name || '',
        memberId: formValue.memberType === 'existing' ? formValue.existingMemberId?.toString() : null,
        userName: formValue.memberType === 'existing' ?
          getMemberFullName(this.members.find(c => c.id === formValue.existingMemberId)!) || 'Member' :
          `${formValue.firstName} ${formValue.lastName}`,
        userEmail: formValue.memberType === 'existing' ?
          this.members.find(c => c.id === formValue.existingMemberId)?.email || '' :
          formValue.email,
        startTime: startDateTime,
        endTime: endDateTime,
        purpose: formValue.purpose,
        status: formValue.status || 'confirmed',
        numberOfPeople: formValue.numberOfPeople || 1,
        notes: formValue.notes || ''
      };

      // Ajouter les informations du nouveau member si nécessaire
      if (formValue.memberType === 'new') {
        reservationData.firstName = formValue.firstName;
        reservationData.lastName = formValue.lastName;
        reservationData.email = formValue.email;
        reservationData.phone = formValue.phone;
        reservationData.company = formValue.company;
      }

      // Appel API pour créer ou mettre à jour la réservation
      const apiCall = this.isEditMode && this.editingReservation?.id ?
        this.reservationService.updateReservation(this.editingReservation.id, reservationData) :
        this.reservationService.createReservation(reservationData);

      apiCall.subscribe({
        next: (savedReservation) => {
          this.savingSignal.set(false);
          this.message.success(
            this.isEditMode ? 'Réservation mise à jour avec succès' : 'Réservation créée avec succès'
          );
          // Rediriger vers la page des réservations après sauvegarde
          this.router.navigate(['/reservations']);
        },
        error: (error) => {
          this.savingSignal.set(false);
          console.error('Erreur lors de la sauvegarde:', error);

          // Gestion des erreurs spécifiques
          if (error.status === 400 && error.error?.message?.includes('Conflicting reservation')) {
            this.message.error('Un conflit de réservation existe pour ce créneau horaire');
          } else if (error.status === 404) {
            this.message.error('Espace ou member introuvable');
          } else {
            this.message.error('Erreur lors de la sauvegarde de la réservation');
          }
        }
      });

      } catch (error: any) {
        this.savingSignal.set(false);
        console.error('Erreur de validation:', error);
        this.message.error(error.message || 'Erreur de validation des données');
      }
    } else {
      this.markFormGroupTouched(this.reservationForm);
    }
  }

  onCancel() {
    this.router.navigate(['/reservations']);
  }

  // Méthode pour le filtrage personnalisé des members (optionnel)
  filterMemberOption = (input: string, option: any): boolean => {
    const member = this.members.find(c => c.id === option.nzValue);
    if (!member) return false;

    const searchText = input.toLowerCase();
    return Boolean(
      member.firstName?.toLowerCase().includes(searchText) ||
      member.lastName?.toLowerCase().includes(searchText) ||
      member.email?.toLowerCase().includes(searchText) ||
      (member.company && member.company.toLowerCase().includes(searchText))
    );
  }

  // Gérer le changement de type de nouveau membre
  onNewMemberTypeChange(memberType: MemberType) {
    const companyControl = this.reservationForm.get('company');
    const iceNumberControl = this.reservationForm.get('iceNumber');
    const studentCodeControl = this.reservationForm.get('studentCode');

    // Réinitialiser tous les champs conditionnels
    companyControl?.clearValidators();
    iceNumberControl?.clearValidators();
    studentCodeControl?.clearValidators();

    // Appliquer les validations selon le type
    if (memberType === MemberType.COMPANY) {
      companyControl?.setValidators([Validators.required]);
      iceNumberControl?.setValidators([Validators.required]);
    } else if (memberType === MemberType.STUDENT) {
      studentCodeControl?.setValidators([Validators.required]);
    }

    // Mettre à jour la validité
    companyControl?.updateValueAndValidity();
    iceNumberControl?.updateValueAndValidity();
    studentCodeControl?.updateValueAndValidity();
  }

  // Validation conditionnelle pour les champs member
  private setupMemberValidation() {
    const memberTypeControl = this.reservationForm.get('memberType');
    const existingMemberIdControl = this.reservationForm.get('existingMemberId');
    const firstNameControl = this.reservationForm.get('firstName');
    const lastNameControl = this.reservationForm.get('lastName');
    const emailControl = this.reservationForm.get('email');

    memberTypeControl?.valueChanges.subscribe(memberType => {
      if (memberType === 'existing') {
        // Member existant : existingMemberId requis
        existingMemberIdControl?.setValidators([Validators.required]);
        firstNameControl?.clearValidators();
        lastNameControl?.clearValidators();
        emailControl?.clearValidators();
      } else if (memberType === 'new') {
        // Nouveau member : téléphone requis
        existingMemberIdControl?.clearValidators();
        firstNameControl?.clearValidators();
        lastNameControl?.clearValidators();
        emailControl?.clearValidators();

        // Rendre le téléphone obligatoire
        const phoneControl = this.reservationForm.get('phone');
        phoneControl?.setValidators([Validators.required]);
        phoneControl?.updateValueAndValidity();
      }

      // Mettre à jour la validation
      existingMemberIdControl?.updateValueAndValidity();
      firstNameControl?.updateValueAndValidity();
      lastNameControl?.updateValueAndValidity();
      emailControl?.updateValueAndValidity();
    });

    // Déclencher la validation initiale
    memberTypeControl?.updateValueAndValidity();
  }

  // Méthodes utilitaires
  private combineDateTime(date: Date | null, time: Date | null): Date {
    if (!date || !time) {
      console.error('Date or time is null/undefined:', { date, time });
      throw new Error('Date and time are required');
    }

    // S'assurer que date et time sont des objets Date valides
    const validDate = date instanceof Date ? date : new Date(date);
    const validTime = time instanceof Date ? time : new Date(time);

    if (isNaN(validDate.getTime()) || isNaN(validTime.getTime())) {
      console.error('Invalid date or time:', { date, time, validDate, validTime });
      throw new Error('Invalid date or time values');
    }

    const combined = new Date(validDate);
    combined.setHours(validTime.getHours(), validTime.getMinutes(), 0, 0);
    return combined;
  }

  private generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  protected readonly EquipmentStatus = EquipmentStatus;
}
