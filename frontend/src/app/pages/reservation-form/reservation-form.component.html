<div class="reservation-form-container">
  <!-- En-tête -->
  <div class="page-header">
    <div class="header-content">
      <h1 class="page-title">
        <nz-icon nzType="calendar" nzTheme="outline"></nz-icon>
        {{ isEditMode ? 'Modifier la réservation' : 'Nouvelle réservation' }}
      </h1>
      <p
        class="page-description">{{ isEditMode ? 'Modifiez les détails de votre réservation' : 'Réservez un espace pour votre activité' }}</p>
    </div>
    <div class="header-actions">
      <button nz-button nzType="default" nzSize="large" (click)="onCancel()" class="cancel-button">
        <nz-icon nzType="arrow-left"></nz-icon>
        Retour
      </button>
    </div>
  </div>

  <!-- Formulaire -->
  <nz-card class="form-card" nzTitle="Détails de la réservation">
    <form nz-form [formGroup]="reservationForm" (ngSubmit)="onSubmit()" class="reservation-form">
      <div nz-row [nzGutter]="16">
        <!-- Sélection de l'espace -->
        <div nz-col nzSpan="24">
          <nz-form-item>
            <nz-form-label [nzRequired]="true">Espace</nz-form-label>
            <nz-form-control nzErrorTip="Veuillez sélectionner un espace">
              <nz-select
                formControlName="spaceId"
                nzPlaceHolder="Sélectionner un espace"
                nzShowSearch
                (ngModelChange)="onSpaceChange($event)"
              >
                <nz-option
                  *ngFor="let space of spaces"
                  [nzLabel]="space.name"
                  [nzValue]="space.id"
                >
                  <div class="space-option">
                    <div class="space-name">{{ space.name }}</div>
                    <div class="space-details">
                      {{ space.location }} • {{ space.capacity }} personnes
                    </div>
                  </div>
                </nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <!-- Sélection du member -->
      <div nz-row [nzGutter]="16">
        <div nz-col nzSpan="24">
          <nz-form-item>
            <nz-form-label [nzRequired]="true">Member</nz-form-label>
            <nz-form-control>
              <nz-radio-group formControlName="memberType">
                <label nz-radio nzValue="existing">Member existant</label>
                <label nz-radio nzValue="new">Nouveau member</label>
              </nz-radio-group>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <!-- Member existant -->
      <div nz-row [nzGutter]="16" *ngIf="reservationForm.get('memberType')?.value === 'existing'">
        <div nz-col nzSpan="24">
          <nz-form-item>
            <nz-form-label [nzRequired]="true">Sélectionner un member</nz-form-label>
            <nz-form-control nzErrorTip="Veuillez sélectionner un member">
              <nz-select
                formControlName="existingMemberId"
                nzPlaceHolder="Rechercher un member..."
                nzShowSearch
                [nzFilterOption]="filterMemberOption"
                style="width: 100%"
              >
                <nz-option
                  *ngFor="let member of members"
                  [nzLabel]="member.firstName + ' ' + member.lastName + ' (' + member.email + ')'"
                  [nzValue]="member.id"
                >
                  <div class="member-option">
                    <div class="member-name">{{ member.firstName }} {{ member.lastName }}</div>
                    <div class="member-details">
                      {{ member.email }} • {{ member.company || 'Particulier' }}
                    </div>
                  </div>
                </nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <!-- Nouveau member -->
      <div *ngIf="reservationForm.get('memberType')?.value === 'new'" class="new-member-section">
        <nz-card class="new-member-card" nzTitle="Informations du nouveau member">
          <div class="new-member-form">

        <div nz-row [nzGutter]="16">
          <div nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label [nzRequired]="true">Prénom</nz-form-label>
              <nz-form-control nzErrorTip="Veuillez saisir le prénom">
                <input nz-input formControlName="firstName" placeholder="Prénom"/>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label [nzRequired]="true">Nom</nz-form-label>
              <nz-form-control nzErrorTip="Veuillez saisir le nom">
                <input nz-input formControlName="lastName" placeholder="Nom"/>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>

        <div nz-row [nzGutter]="16">
          <div nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label [nzRequired]="false">Email</nz-form-label>
              <nz-form-control>
                <input nz-input formControlName="email" placeholder="<EMAIL>" type="email"/>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label [nzRequired]="true">Téléphone</nz-form-label>
              <nz-form-control nzErrorTip="Veuillez saisir un numéro de téléphone">
                <input nz-input formControlName="phone" placeholder="+33 1 23 45 67 89"/>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>

        <!-- Champs conditionnels selon le type de membre -->
        <div nz-row [nzGutter]="16" *ngIf="reservationForm.get('newMemberType')?.value === MemberType.COMPANY">
          <div nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label [nzRequired]="true">Nom de l'entreprise</nz-form-label>
              <nz-form-control nzErrorTip="Veuillez saisir le nom de l'entreprise">
                <input nz-input formControlName="company" placeholder="Nom de l'entreprise"/>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label [nzRequired]="true">Numéro ICE</nz-form-label>
              <nz-form-control nzErrorTip="Veuillez saisir le numéro ICE">
                <input nz-input formControlName="iceNumber" placeholder="Numéro ICE"/>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>

        <div nz-row [nzGutter]="16" *ngIf="reservationForm.get('newMemberType')?.value === MemberType.STUDENT">
          <div nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label [nzRequired]="true">Code étudiant</nz-form-label>
              <nz-form-control nzErrorTip="Veuillez saisir le code étudiant">
                <input nz-input formControlName="studentCode" placeholder="Code étudiant"/>
              </nz-form-control>
            </nz-form-item>
          </div>
          <div nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label [nzRequired]="true">Type</nz-form-label>
              <nz-form-control nzErrorTip="Veuillez sélectionner un type">
                <nz-select formControlName="newMemberType" nzPlaceHolder="Sélectionner le type" (ngModelChange)="onNewMemberTypeChange($event)">
                  <nz-option [nzValue]="MemberType.STUDENT" nzLabel="Étudiant"></nz-option>
                  <nz-option [nzValue]="MemberType.PROFESSIONAL" nzLabel="Professionnel"></nz-option>
                  <nz-option [nzValue]="MemberType.COMPANY" nzLabel="Entreprise"></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>

        <div nz-row [nzGutter]="16">
          <div nz-col nzSpan="12">
            <nz-form-item>
              <nz-form-label>Abonnement</nz-form-label>
              <nz-form-control>
                <nz-select formControlName="subscriptionType" nzPlaceHolder="Type d'abonnement">
                  <nz-option nzLabel="Basic" nzValue="Basic"></nz-option>
                  <nz-option nzLabel="Standard" nzValue="Standard"></nz-option>
                  <nz-option nzLabel="Premium" nzValue="Premium"></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>
          </div>
        </nz-card>
      </div>

      <div nz-row [nzGutter]="16">
        <!-- Date et heure de début -->
        <div nz-col nzSpan="12">
          <nz-form-item>
            <nz-form-label [nzRequired]="true">Date et heure de début</nz-form-label>
            <nz-form-control nzErrorTip="Veuillez sélectionner la date et heure de début">
              <nz-date-picker
                formControlName="startDate"
                nzShowTime
                nzFormat="dd/MM/yyyy HH:mm"
                nzPlaceHolder="Sélectionner la date et heure de début"
                [nzDisabledDate]="disabledDate"
                (ngModelChange)="calculateDurationFromDates()"
                style="width: 100%"
              ></nz-date-picker>
            </nz-form-control>
          </nz-form-item>
        </div>

        <!-- Date et heure de fin -->
        <div nz-col nzSpan="12">
          <nz-form-item>
            <nz-form-label [nzRequired]="true">Date et heure de fin</nz-form-label>
            <nz-form-control nzErrorTip="Veuillez sélectionner la date et heure de fin">
              <nz-date-picker
                formControlName="endDate"
                nzShowTime
                nzFormat="dd/MM/yyyy HH:mm"
                nzPlaceHolder="Sélectionner la date et heure de fin"
                [nzDisabledDate]="disabledEndDate"
                (ngModelChange)="calculateDurationFromDates()"
                style="width: 100%"
              ></nz-date-picker>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <!-- Nombre de personnes -->
      <div nz-row [nzGutter]="16">
        <div nz-col nzSpan="12">
          <nz-form-item>
            <nz-form-label [nzRequired]="true">Nombre de personnes</nz-form-label>
            <nz-form-control nzErrorTip="Veuillez indiquer le nombre de personnes">
              <nz-input-number
                formControlName="numberOfPeople"
                [nzMin]="1"
                [nzMax]="selectedSpace?.capacity || 50"
                nzPlaceHolder="Nombre de personnes"
                style="width: 100%"
              ></nz-input-number>
              <div class="capacity-info" *ngIf="selectedSpace">
                <small>Capacité maximale : {{ selectedSpace.capacity }} personnes</small>
              </div>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <!-- Durée calculée -->
      <div nz-row [nzGutter]="16" *ngIf="getDurationText()">
        <div nz-col nzSpan="24">
          <nz-alert
            nzType="info"
            [nzMessage]="getDurationText()"
            nzShowIcon
            style="margin-bottom: 16px;"
          ></nz-alert>
        </div>
      </div>

      <!-- Durée -->
      <div nz-row [nzGutter]="16">
        <div nz-col nzSpan="24">
          <nz-form-item>
            <nz-form-label [nzRequired]="true">Durée</nz-form-label>
            <nz-form-control nzErrorTip="Veuillez sélectionner une durée valide">
              <div class="duration-inputs">
                <div class="duration-group">
                  <nz-input-number
                    formControlName="durationMonths"
                    [nzMin]="0"
                    [nzMax]="12"
                    [nzStep]="1"
                    nzPlaceHolder="0"
                    [nzDisabled]="true"
                    class="duration-input"
                  ></nz-input-number>
                  <span class="duration-label">mois</span>
                </div>

                <div class="duration-group">
                  <nz-input-number
                    formControlName="durationDays"
                    [nzMin]="0"
                    [nzMax]="31"
                    [nzStep]="1"
                    nzPlaceHolder="0"
                    [nzDisabled]="true"
                    class="duration-input"
                  ></nz-input-number>
                  <span class="duration-label">jours</span>
                </div>

                <div class="duration-group">
                  <nz-input-number
                    formControlName="durationHours"
                    [nzMin]="0"
                    [nzMax]="23"
                    [nzStep]="1"
                    nzPlaceHolder="0"
                    [nzDisabled]="true"
                    class="duration-input"
                  ></nz-input-number>
                  <span class="duration-label">heures</span>
                </div>

                <div class="duration-group">
                  <nz-select
                    formControlName="durationMinutes"
                    nzPlaceHolder="0"
                    [nzDisabled]="true"
                    class="duration-select"
                  >
                    <nz-option nzLabel="0 min" [nzValue]="0"></nz-option>
                    <nz-option nzLabel="15 min" [nzValue]="15"></nz-option>
                    <nz-option nzLabel="30 min" [nzValue]="30"></nz-option>
                    <nz-option nzLabel="45 min" [nzValue]="45"></nz-option>
                  </nz-select>
                </div>

                <div class="duration-total" *ngIf="getTotalDurationText()">
                  <span class="total-label">Durée calculée: {{ getTotalDurationText() }}</span>
                </div>
              </div>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>



        <!-- Objet et récurrence -->
        <div nz-row [nzGutter]="16">
          <div nz-col nzSpan="16">
            <nz-form-item>
              <nz-form-label [nzRequired]="false">Objet de la réservation</nz-form-label>
              <nz-form-control>
                <input
                  nz-input
                  formControlName="purpose"
                  placeholder="Ex: Réunion équipe, Formation, Entretien member..."
                />
              </nz-form-control>
            </nz-form-item>
          </div>

          <div nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label [nzRequired]="true">Statut</nz-form-label>
              <nz-form-control>
                <nz-select formControlName="status" nzPlaceHolder="Sélectionner le statut">
                  <nz-option
                    *ngFor="let option of statusOptions"
                    [nzValue]="option.value"
                    [nzLabel]="option.label">
                  </nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </div>

          <div nz-col nzSpan="8">
            <nz-form-item>
              <nz-form-label>Récurrence</nz-form-label>
              <nz-form-control>
                <nz-select
                  formControlName="recurrence"
                  nzPlaceHolder="Aucune"
                  nzAllowClear
                >
                  <nz-option nzLabel="Aucune" nzValue="none"></nz-option>
                  <nz-option nzLabel="Quotidienne" nzValue="daily"></nz-option>
                  <nz-option nzLabel="Hebdomadaire" nzValue="weekly"></nz-option>
                  <nz-option nzLabel="Mensuelle" nzValue="monthly"></nz-option>
                </nz-select>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>

        <!-- Notes additionnelles -->
        <div nz-row [nzGutter]="16">
          <div nz-col nzSpan="24">
            <nz-form-item>
              <nz-form-label>Notes additionnelles</nz-form-label>
              <nz-form-control>
                <textarea
                  nz-input
                  formControlName="notes"
                  placeholder="Informations complémentaires, besoins spécifiques..."
                  rows="3"
                ></textarea>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>



        <!-- Informations sur l'espace sélectionné -->
        <nz-card
          *ngIf="selectedSpace"
          class="space-info-card"
          nzTitle="Informations sur l'espace"
          nzSize="small"
        >
          <div class="space-info">
            <div class="info-item">
              <nz-icon nzType="environment" nzTheme="outline"></nz-icon>
              <span>{{ selectedSpace.location }}</span>
            </div>
            <div class="info-item">
              <nz-icon nzType="team" nzTheme="outline"></nz-icon>
              <span>{{ selectedSpace.capacity }} personnes</span>
            </div>
            <div class="info-item">
              <nz-icon nzType="euro" nzTheme="outline"></nz-icon>
              <span>{{ selectedSpace.pricing.hourlyRate }} {{ selectedSpace.pricing.currency || 'MAD' }}/h</span>
            </div>
            <div class="info-item" *ngIf="selectedSpace.pricing.dailyRate && selectedSpace.pricing.dailyRate > 0">
              <nz-icon nzType="euro" nzTheme="outline"></nz-icon>
              <span>{{ selectedSpace.pricing.dailyRate }} {{ selectedSpace.pricing.currency || 'MAD' }}/jour</span>
            </div>
            <div class="info-item" *ngIf="selectedSpace.pricing.weeklyRate && selectedSpace.pricing.weeklyRate > 0">
              <nz-icon nzType="euro" nzTheme="outline"></nz-icon>
              <span>{{ selectedSpace.pricing.weeklyRate }} {{ selectedSpace.pricing.currency || 'MAD' }}/semaine</span>
            </div>
            <div class="info-item" *ngIf="selectedSpace.pricing.monthlyRate && selectedSpace.pricing.monthlyRate > 0">
              <nz-icon nzType="euro" nzTheme="outline"></nz-icon>
              <span>{{ selectedSpace.pricing.monthlyRate }} {{ selectedSpace.pricing.currency || 'MAD' }}/mois</span>
            </div>
          </div>

          <!-- Équipements disponibles -->
          <div class="equipment-list" *ngIf="selectedSpace.equipment.length > 0">
            <h4>Équipements disponibles :</h4>
            <nz-tag
              *ngFor="let equipment of selectedSpace.equipment"
              [nzColor]="equipment.status === EquipmentStatus.WORKING ? 'green' : 'orange'"
            >
              {{ equipment.name }}
            </nz-tag>
          </div>
        </nz-card>

        <!-- Actions -->
        <div class="form-actions">
          <button nz-button nzType="default" (click)="onCancel()">
            Annuler
          </button>
          <button
            nz-button
            nzType="primary"
            type="submit"
            [nzLoading]="saving"
            [disabled]="reservationForm.invalid"
          >
            {{ isEditMode ? 'Modifier' : 'Réserver' }}
          </button>
        </div>
    </form>
  </nz-card>
</div>
