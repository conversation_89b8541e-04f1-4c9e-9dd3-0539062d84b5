package com.workeem.workeem_api.business.client.application;

import com.workeem.workeem_api.business.client.domain.Member;

import java.util.List;
import java.util.Optional;

public interface MemberService {

    /**
     * Get all clients for a specific site
     */
    List<Member> getAllClients(Long siteId);

    /**
     * Get client by ID
     */
    Member getClientById(Long id);

    /**
     * Get client by email and site
     */
    Optional<Member> getClientByEmail(Long siteId, String email);

    /**
     * Create a new client
     */
    Member createClient(Member member);

    /**
     * Update an existing client
     */
    Member updateClient(Long clientId, Member member);

    /**
     * Delete a client
     */
    void deleteClient(Long id);

    /**
     * Check if email exists in site
     */
    boolean emailExists(Long siteId, String email);

    /**
     * Count clients by site
     */
    long countClientsBySite(Long siteId);
}
