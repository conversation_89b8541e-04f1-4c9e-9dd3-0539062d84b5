package com.workeem.workeem_api.business.subscription.application;

import com.workeem.workeem_api.business.member.domain.MemberType;
import com.workeem.workeem_api.business.subscription.domain.Subscription;
import com.workeem.workeem_api.business.subscription.infrastructure.SubscriptionRepository;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class SubscriptionServiceImpl implements SubscriptionService {

    private final SubscriptionRepository subscriptionRepository;

    @Override
    @Transactional(readOnly = true)
    public List<Subscription> getAllSubscriptions(Long siteId) {
        log.debug("Fetching all subscriptions for site: {}", siteId);
        return subscriptionRepository.findBySiteId(siteId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Subscription> getActiveSubscriptions(Long siteId) {
        log.debug("Fetching active subscriptions for site: {}", siteId);
        return subscriptionRepository.findBySiteIdAndIsActive(siteId, true);
    }

    @Override
    @Transactional(readOnly = true)
    public Subscription getSubscriptionById(Long id) {
        log.debug("Fetching subscription with ID: {}", id);
        return subscriptionRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Subscription with ID " + id + " not found"));
    }

    @Override
    @Transactional(readOnly = true)
    public List<Subscription> getSubscriptionsByMemberType(Long siteId, MemberType memberType) {
        log.debug("Fetching subscriptions for site: {} and member type: {}", siteId, memberType);
        return subscriptionRepository.findBySiteIdAndMemberType(siteId, memberType);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Subscription> getActiveSubscriptionsByMemberType(Long siteId, MemberType memberType) {
        log.debug("Fetching active subscriptions for site: {} and member type: {}", siteId, memberType);
        return subscriptionRepository.findBySiteIdAndMemberTypeAndIsActive(siteId, memberType, true);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Subscription> getCompatibleSubscriptions(Long siteId, MemberType memberType) {
        log.debug("Fetching compatible subscriptions for site: {} and member type: {}", siteId, memberType);
        
        List<Subscription> compatibleSubscriptions = new ArrayList<>();
        
        // Ajouter les abonnements spécifiques au type de membre
        compatibleSubscriptions.addAll(
            subscriptionRepository.findBySiteIdAndMemberTypeAndIsActive(siteId, memberType, true)
        );
        
        // Ajouter les abonnements universels (memberType = null)
        compatibleSubscriptions.addAll(
            subscriptionRepository.findBySiteIdAndMemberTypeIsNullAndIsActive(siteId, true)
        );
        
        return compatibleSubscriptions;
    }

    @Override
    public Subscription createSubscription(Subscription subscription) {
        log.debug("Creating new subscription: {} for site: {}", 
                  subscription.getName(), subscription.getSiteId());
        return subscriptionRepository.save(subscription);
    }

    @Override
    public Subscription updateSubscription(Long subscriptionId, Subscription subscription) {
        log.debug("Updating subscription with ID: {}", subscriptionId);

        Subscription existingSubscription = getSubscriptionById(subscriptionId);

        // Mettre à jour les champs
        existingSubscription.setName(subscription.getName());
        existingSubscription.setDescription(subscription.getDescription());
        existingSubscription.setPrice(subscription.getPrice());
        existingSubscription.setCurrency(subscription.getCurrency());
        existingSubscription.setDurationDays(subscription.getDurationDays());
        existingSubscription.setMemberType(subscription.getMemberType());
        existingSubscription.setFeatures(subscription.getFeatures());
        existingSubscription.setMaxReservationsPerDay(subscription.getMaxReservationsPerDay());
        existingSubscription.setMaxReservationsPerWeek(subscription.getMaxReservationsPerWeek());
        existingSubscription.setMaxReservationsPerMonth(subscription.getMaxReservationsPerMonth());
        existingSubscription.setMaxConsecutiveHours(subscription.getMaxConsecutiveHours());
        existingSubscription.setAdvanceBookingDays(subscription.getAdvanceBookingDays());
        existingSubscription.setCanBookMeetingRooms(subscription.getCanBookMeetingRooms());
        existingSubscription.setCanAccessPremiumAreas(subscription.getCanAccessPremiumAreas());
        existingSubscription.setIsActive(subscription.getIsActive());

        return subscriptionRepository.save(existingSubscription);
    }

    @Override
    public void deleteSubscription(Long id) {
        log.debug("Deleting subscription with ID: {}", id);

        Subscription subscription = getSubscriptionById(id);
        subscriptionRepository.delete(subscription);
    }

    @Override
    @Transactional(readOnly = true)
    public long countSubscriptionsBySite(Long siteId) {
        return subscriptionRepository.countBySiteId(siteId);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Subscription> getAllSubscriptions(Long siteId, Pageable pageable) {
        log.debug("Fetching all subscriptions for site: {} with pagination: {}", siteId, pageable);
        return subscriptionRepository.findBySiteId(siteId, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Subscription> getActiveSubscriptions(Long siteId, Pageable pageable) {
        log.debug("Fetching active subscriptions for site: {} with pagination: {}", siteId, pageable);
        return subscriptionRepository.findBySiteIdAndIsActive(siteId, true, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Subscription> getSubscriptionsByMemberType(Long siteId, MemberType memberType, Pageable pageable) {
        log.debug("Fetching subscriptions by member type: {} for site: {} with pagination: {}", memberType, siteId, pageable);
        return subscriptionRepository.findBySiteIdAndMemberType(siteId, memberType, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Subscription> getActiveSubscriptionsByMemberType(Long siteId, MemberType memberType, Pageable pageable) {
        log.debug("Fetching active subscriptions by member type: {} for site: {} with pagination: {}", memberType, siteId, pageable);
        return subscriptionRepository.findBySiteIdAndMemberTypeAndIsActive(siteId, memberType, true, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Subscription> searchSubscriptions(Long siteId, String searchTerm, Boolean active, MemberType memberType, Pageable pageable) {
        log.debug("Searching subscriptions with term: '{}' for site: {} with filters - active: {}, memberType: {}, pagination: {}",
                  searchTerm, siteId, active, memberType, pageable);
        return subscriptionRepository.searchSubscriptions(siteId, searchTerm, active, memberType, pageable);
    }
}
