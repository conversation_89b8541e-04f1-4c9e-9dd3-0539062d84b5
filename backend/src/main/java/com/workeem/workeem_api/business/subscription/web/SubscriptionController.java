package com.workeem.workeem_api.business.subscription.web;

import com.workeem.workeem_api.business.member.domain.MemberType;
import com.workeem.workeem_api.business.subscription.application.SubscriptionService;
import com.workeem.workeem_api.business.subscription.domain.Subscription;
import com.workeem.workeem_api.business.subscription.web.dto.SubscriptionRequestDto;
import com.workeem.workeem_api.business.subscription.web.dto.SubscriptionResponseDto;
import com.workeem.workeem_api.shared.dto.PageRequestDto;
import com.workeem.workeem_api.shared.dto.PageResponseDto;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/subscriptions")
@RequiredArgsConstructor
@Slf4j
public class SubscriptionController {

    private final SubscriptionService subscriptionService;

    /**
     * Get all subscriptions with optional filters and pagination
     */
    @GetMapping
    public ResponseEntity<PageResponseDto<SubscriptionResponseDto>> getSubscriptions(
            @RequestParam Long siteId,
            @RequestParam(required = false) Boolean active,
            @RequestParam(required = false) MemberType memberType,
            @RequestParam(required = false) String search,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String sortBy,
            @RequestParam(defaultValue = "ASC") String sortDirection) {

        log.debug("GET /api/subscriptions - siteId: {}, active: {}, memberType: {}, search: {}, page: {}, size: {}",
                  siteId, active, memberType, search, page, size);

        // Créer l'objet de pagination
        PageRequestDto pageRequest = PageRequestDto.builder()
                .page(page)
                .size(size)
                .sortBy(sortBy)
                .sortDirection(sortDirection)
                .build();
        pageRequest.validate();

        Page<Subscription> subscriptionPage;

        // Si un terme de recherche est fourni, utiliser la recherche
        if (search != null && !search.trim().isEmpty()) {
            subscriptionPage = subscriptionService.searchSubscriptions(siteId, search.trim(), active, memberType, pageRequest.toPageable());
        } else {
            // Logique existante sans recherche
            if (memberType != null) {
                if (active != null && active) {
                    subscriptionPage = subscriptionService.getActiveSubscriptionsByMemberType(siteId, memberType, pageRequest.toPageable());
                } else {
                    subscriptionPage = subscriptionService.getSubscriptionsByMemberType(siteId, memberType, pageRequest.toPageable());
                }
            } else {
                if (active != null && active) {
                    subscriptionPage = subscriptionService.getActiveSubscriptions(siteId, pageRequest.toPageable());
                } else {
                    subscriptionPage = subscriptionService.getAllSubscriptions(siteId, pageRequest.toPageable());
                }
            }
        }

        // Convertir en DTOs
        Page<SubscriptionResponseDto> subscriptionDtoPage = subscriptionPage.map(SubscriptionResponseDto::fromEntity);

        // Créer la réponse paginée
        PageResponseDto<SubscriptionResponseDto> response = PageResponseDto.fromPage(subscriptionDtoPage);

        return ResponseEntity.ok(response);
    }

    /**
     * Get compatible subscriptions for a member type
     */
    @GetMapping("/compatible")
    public ResponseEntity<List<SubscriptionResponseDto>> getCompatibleSubscriptions(
            @RequestParam Long siteId,
            @RequestParam MemberType memberType) {
        
        log.debug("GET /api/subscriptions/compatible - siteId: {}, memberType: {}", siteId, memberType);

        List<Subscription> subscriptions = subscriptionService.getCompatibleSubscriptions(siteId, memberType);
        List<SubscriptionResponseDto> subscriptionDtos = subscriptions.stream()
                .map(SubscriptionResponseDto::fromEntity)
                .collect(Collectors.toList());

        return ResponseEntity.ok(subscriptionDtos);
    }

    /**
     * Get subscription by ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<SubscriptionResponseDto> getSubscriptionById(@PathVariable Long id) {
        log.debug("GET /api/subscriptions/{}", id);

        Subscription subscription = subscriptionService.getSubscriptionById(id);
        SubscriptionResponseDto responseDto = SubscriptionResponseDto.fromEntity(subscription);

        return ResponseEntity.ok(responseDto);
    }

    /**
     * Create a new subscription
     */
    @PostMapping
    public ResponseEntity<SubscriptionResponseDto> createSubscription(
            @RequestParam Long siteId,
            @Valid @RequestBody SubscriptionRequestDto subscriptionRequestDto) {
        log.debug("POST /api/subscriptions - Creating subscription for site: {}", siteId);

        Subscription subscription = subscriptionRequestDto.toEntity(siteId);
        Subscription createdSubscription = subscriptionService.createSubscription(subscription);
        SubscriptionResponseDto responseDto = SubscriptionResponseDto.fromEntity(createdSubscription);

        return ResponseEntity.status(HttpStatus.CREATED).body(responseDto);
    }

    /**
     * Update an existing subscription
     */
    @PutMapping("/{id}")
    public ResponseEntity<SubscriptionResponseDto> updateSubscription(
            @PathVariable Long id,
            @Valid @RequestBody SubscriptionRequestDto subscriptionRequestDto) {
        log.debug("PUT /api/subscriptions/{}", id);

        Subscription subscription = subscriptionRequestDto.toEntity(null); // siteId will be preserved from existing subscription
        Subscription updatedSubscription = subscriptionService.updateSubscription(id, subscription);
        SubscriptionResponseDto responseDto = SubscriptionResponseDto.fromEntity(updatedSubscription);

        return ResponseEntity.ok(responseDto);
    }

    /**
     * Delete a subscription
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteSubscription(@PathVariable Long id) {
        log.debug("DELETE /api/subscriptions/{}", id);

        subscriptionService.deleteSubscription(id);
        return ResponseEntity.noContent().build();
    }
}
