package com.workeem.workeem_api.business.reservation.web.dto;

import com.workeem.workeem_api.business.reservation.domain.Reservation;
import com.workeem.workeem_api.business.reservation.domain.ReservationStatus;
import com.workeem.workeem_api.business.reservation.domain.RecurrenceType;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReservationRequestDto {

    @NotNull(message = "Space ID is required")
    private Long spaceId;

    private Long memberId; // Optionnel - si null, un nouveau member sera créé

    // Champs pour créer un nouveau member (utilisés si memberId est null)
    private String firstName;
    private String lastName;
    private String email;
    private String phone;
    private String company;

    @NotNull(message = "Start time is required")
    private Instant startTime;

    @NotNull(message = "End time is required")
    private Instant endTime;

    private String purpose;

    private ReservationStatus status = ReservationStatus.PENDING;

    @Positive(message = "Number of people must be positive")
    private Integer numberOfPeople = 1;

    private String notes;

    private RecurrenceType recurrence = RecurrenceType.NONE;

    private Double totalCost;

    /**
     * Convert DTO to Entity (relations will be set by the service)
     */
    public Reservation toEntity(Long siteId) {
        Reservation reservation = new Reservation();
        reservation.setSiteId(siteId);
        // Les relations Space et Member seront définies par le service

        // Convertir Instant en LocalDateTime (utilise la timezone système par défaut)
        reservation.setStartTime(this.startTime != null ?
            LocalDateTime.ofInstant(this.startTime, ZoneId.systemDefault()) : null);
        reservation.setEndTime(this.endTime != null ?
            LocalDateTime.ofInstant(this.endTime, ZoneId.systemDefault()) : null);

        reservation.setPurpose(this.purpose);
        reservation.setStatus(this.status != null ? this.status : ReservationStatus.PENDING);
        reservation.setNumberOfPeople(this.numberOfPeople != null ? this.numberOfPeople : 1);
        reservation.setNotes(this.notes);
        reservation.setRecurrence(this.recurrence != null ? this.recurrence : RecurrenceType.NONE);
        reservation.setTotalCost(this.totalCost);
        return reservation;
    }
}
