package com.workeem.workeem_api.business.subscription.application;

import com.workeem.workeem_api.business.member.domain.MemberType;
import com.workeem.workeem_api.business.subscription.domain.Subscription;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface SubscriptionService {

    /**
     * Get all subscriptions for a specific site
     */
    List<Subscription> getAllSubscriptions(Long siteId);

    /**
     * Get active subscriptions for a specific site
     */
    List<Subscription> getActiveSubscriptions(Long siteId);

    /**
     * Get subscription by ID
     */
    Subscription getSubscriptionById(Long id);

    /**
     * Get subscriptions by member type for a specific site
     */
    List<Subscription> getSubscriptionsByMemberType(Long siteId, MemberType memberType);

    /**
     * Get active subscriptions by member type for a specific site
     */
    List<Subscription> getActiveSubscriptionsByMemberType(Long siteId, MemberType memberType);

    /**
     * Get subscriptions compatible with a member type (including universal subscriptions)
     */
    List<Subscription> getCompatibleSubscriptions(Long siteId, MemberType memberType);

    /**
     * Create a new subscription
     */
    Subscription createSubscription(Subscription subscription);

    /**
     * Update an existing subscription
     */
    Subscription updateSubscription(Long subscriptionId, Subscription subscription);

    /**
     * Delete a subscription
     */
    void deleteSubscription(Long id);

    /**
     * Count subscriptions by site
     */
    long countSubscriptionsBySite(Long siteId);

    /**
     * Get all subscriptions for a specific site with pagination
     */
    Page<Subscription> getAllSubscriptions(Long siteId, Pageable pageable);

    /**
     * Get active subscriptions for a specific site with pagination
     */
    Page<Subscription> getActiveSubscriptions(Long siteId, Pageable pageable);

    /**
     * Get subscriptions by member type for a specific site with pagination
     */
    Page<Subscription> getSubscriptionsByMemberType(Long siteId, MemberType memberType, Pageable pageable);

    /**
     * Get active subscriptions by member type for a specific site with pagination
     */
    Page<Subscription> getActiveSubscriptionsByMemberType(Long siteId, MemberType memberType, Pageable pageable);

    /**
     * Search subscriptions by name or description with optional filters
     */
    Page<Subscription> searchSubscriptions(Long siteId, String searchTerm, Boolean active, MemberType memberType, Pageable pageable);
}
