package com.workeem.workeem_api.business.client.infrastructure;

import com.workeem.workeem_api.business.client.domain.Member;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface MemberRepository extends JpaRepository<Member, Long> {

    /**
     * Find all clients for a specific site
     */
    List<Member> findBySiteId(Long siteId);

    /**
     * Find active clients for a specific site
     */
    List<Member> findBySiteIdAndIsActive(Long siteId, Boolean isActive);

    /**
     * Find client by email and site
     */
    Optional<Member> findBySiteIdAndEmail(Long siteId, String email);

    /**
     * Check if email exists in site
     */
    boolean existsBySiteIdAndEmail(Long siteId, String email);

    /**
     * Count clients by site
     */
    long countBySiteId(Long siteId);
}
