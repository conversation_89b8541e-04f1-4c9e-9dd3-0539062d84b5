package com.workeem.workeem_api.business.client.web;

import com.workeem.workeem_api.business.client.application.MemberService;
import com.workeem.workeem_api.business.client.domain.Member;
import com.workeem.workeem_api.business.client.web.dto.MemberRequestDto;
import com.workeem.workeem_api.business.client.web.dto.MemberResponseDto;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/clients")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "http://localhost:4200")
public class MemberController implements Serializable {

    private final MemberService memberService;

    /**
     * Get all clients with optional filters
     */
    @GetMapping
    public ResponseEntity<List<MemberResponseDto>> getClients(
            @RequestParam Long siteId,
            @RequestParam(required = false) Boolean active) {
        
        log.debug("GET /api/clients - siteId: {}, active: {}", siteId, active);

        List<Member> members;
        if (active != null && active) {
            members = memberService.getAllClients(siteId).stream()
                    .filter(Member::getIsActive)
                    .collect(Collectors.toList());
        } else {
            members = memberService.getAllClients(siteId);
        }

        List<MemberResponseDto> clientDtos = members.stream()
                .map(MemberResponseDto::fromEntity)
                .collect(Collectors.toList());

        return ResponseEntity.ok(clientDtos);
    }

    /**
     * Get client by ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<MemberResponseDto> getClientById(@PathVariable Long id) {
        log.debug("GET /api/clients/{}", id);

        Member member = memberService.getClientById(id);
        MemberResponseDto clientDto = MemberResponseDto.fromEntity(member);
        
        return ResponseEntity.ok(clientDto);
    }

    /**
     * Create a new client
     */
    @PostMapping
    public ResponseEntity<MemberResponseDto> createClient(
            @RequestParam Long siteId,
            @Valid @RequestBody MemberRequestDto memberRequestDto) {
        log.debug("POST /api/clients - Creating client for site: {}", siteId);

        Member member = memberRequestDto.toEntity(siteId);
        Member createdMember = memberService.createClient(member);
        MemberResponseDto responseDto = MemberResponseDto.fromEntity(createdMember);

        return ResponseEntity.status(HttpStatus.CREATED).body(responseDto);
    }

    /**
     * Update an existing client
     */
    @PutMapping("/{id}")
    public ResponseEntity<MemberResponseDto> updateClient(
            @PathVariable Long id,
            @Valid @RequestBody MemberRequestDto memberRequestDto) {
        log.debug("PUT /api/clients/{}", id);

        Member member = memberRequestDto.toEntity(null); // siteId will be preserved from existing client
        Member updatedMember = memberService.updateClient(id, member);
        MemberResponseDto responseDto = MemberResponseDto.fromEntity(updatedMember);

        return ResponseEntity.ok(responseDto);
    }

    /**
     * Delete a client
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteClient(@PathVariable Long id) {
        log.debug("DELETE /api/clients/{}", id);

        memberService.deleteClient(id);
        return ResponseEntity.noContent().build();
    }

    /**
     * Get clients count for a site
     */
    @GetMapping("/count")
    public ResponseEntity<Long> getClientsCount(@RequestParam Long siteId) {
        log.debug("GET /api/clients/count - siteId: {}", siteId);

        long count = memberService.countClientsBySite(siteId);
        return ResponseEntity.ok(count);
    }
}
