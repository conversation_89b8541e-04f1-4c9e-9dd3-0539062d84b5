package com.workeem.workeem_api.business.space.web;

import com.workeem.workeem_api.business.space.application.SpaceService;
import com.workeem.workeem_api.business.space.domain.Space;
import com.workeem.workeem_api.business.space.domain.SpaceType;
import com.workeem.workeem_api.business.space.web.dto.EquipmentDto;
import com.workeem.workeem_api.business.space.web.dto.SpaceAvailabilityDto;
import com.workeem.workeem_api.business.space.web.dto.SpaceRequestDto;
import com.workeem.workeem_api.business.space.web.dto.SpaceResponseDto;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/spaces")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "http://localhost:4200")
public class SpaceController implements Serializable {

    private final SpaceService spaceService;

    /**
     * Get all spaces for a specific site
     */
    @GetMapping
    public ResponseEntity<List<SpaceResponseDto>> getAllSpaces(
            @RequestParam String siteId,
            @RequestParam(required = false) Boolean activeOnly) {
        log.debug("GET /api/spaces - siteId: {}, activeOnly: {}", siteId, activeOnly);

        List<Space> spaces;
        if (Boolean.TRUE.equals(activeOnly)) {
            spaces = spaceService.getActiveSpaces(siteId);
        } else {
            spaces = spaceService.getAllSpaces(siteId);
        }

        List<SpaceResponseDto> spaceDtos = spaces.stream()
                .map(space -> {
                    List<EquipmentDto> equipment = spaceService.getEquipmentBySpaceId(space.getSpaceId());
                    SpaceAvailabilityDto availability = spaceService.getAvailabilityBySpaceId(space.getSpaceId());
                    return SpaceResponseDto.fromEntity(space, equipment, availability);
                })
                .collect(Collectors.toList());

        return ResponseEntity.ok(spaceDtos);
    }

    /**
     * Get space by ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<SpaceResponseDto> getSpaceById(@PathVariable Long id) {
        log.debug("GET /api/spaces/{}", id);
        
        Space space = spaceService.getSpaceById(id);
        List<EquipmentDto> equipment = spaceService.getEquipmentBySpaceId(space.getSpaceId());
        SpaceAvailabilityDto availability = spaceService.getAvailabilityBySpaceId(space.getSpaceId());
        SpaceResponseDto spaceDto = SpaceResponseDto.fromEntity(space, equipment, availability);
        return ResponseEntity.ok(spaceDto);
    }

    /**
     * Get spaces by type for a specific site
     */
    @GetMapping("/by-type")
    public ResponseEntity<List<SpaceResponseDto>> getSpacesByType(
            @RequestParam String siteId,
            @RequestParam SpaceType type) {
        log.debug("GET /api/spaces/by-type - siteId: {}, type: {}", siteId, type);

        List<Space> spaces = spaceService.getSpacesByType(siteId, type);
        List<SpaceResponseDto> spaceDtos = spaces.stream()
                .map(space -> {
                    List<EquipmentDto> equipment = spaceService.getEquipmentBySpaceId(space.getSpaceId());
                    SpaceAvailabilityDto availability = spaceService.getAvailabilityBySpaceId(space.getSpaceId());
                    return SpaceResponseDto.fromEntity(space, equipment, availability);
                })
                .collect(Collectors.toList());

        return ResponseEntity.ok(spaceDtos);
    }

    /**
     * Create a new space
     */
    @PostMapping
    public ResponseEntity<SpaceResponseDto> createSpace(
            @RequestParam String siteId,
            @Valid @RequestBody SpaceRequestDto spaceRequestDto) {
        log.debug("POST /api/spaces - Creating space: {} for site: {}", spaceRequestDto.getName(), siteId);
        
        Space space = spaceRequestDto.toEntity(siteId);
        Space createdSpace = spaceService.createSpace(space, spaceRequestDto.getEquipmentDtos(), spaceRequestDto.getAvailabilityDto());
        List<EquipmentDto> equipment = spaceService.getEquipmentBySpaceId(createdSpace.getSpaceId());
        SpaceAvailabilityDto availability = spaceService.getAvailabilityBySpaceId(createdSpace.getSpaceId());
        SpaceResponseDto responseDto = SpaceResponseDto.fromEntity(createdSpace, equipment, availability);
        
        return ResponseEntity.status(HttpStatus.CREATED).body(responseDto);
    }

    /**
     * Update an existing space
     */
    @PutMapping("/{id}")
    public ResponseEntity<SpaceResponseDto> updateSpace(
            @PathVariable Long id,
            @RequestParam String siteId,
            @Valid @RequestBody SpaceRequestDto spaceRequestDto) {
        log.debug("PUT /api/spaces/{} - Updating space for site: {}", id, siteId);
        
        Space space = spaceRequestDto.toEntity(siteId);
        Space updatedSpace = spaceService.updateSpace(id, space, spaceRequestDto.getEquipmentDtos(), spaceRequestDto.getAvailabilityDto());
        List<EquipmentDto> equipment = spaceService.getEquipmentBySpaceId(updatedSpace.getSpaceId());
        SpaceAvailabilityDto availability = spaceService.getAvailabilityBySpaceId(updatedSpace.getSpaceId());
        SpaceResponseDto responseDto = SpaceResponseDto.fromEntity(updatedSpace, equipment, availability);
        
        return ResponseEntity.ok(responseDto);
    }

    /**
     * Delete a space
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteSpace(@PathVariable Long id) {
        log.debug("DELETE /api/spaces/{}", id);
        
        spaceService.deleteSpace(id);
        return ResponseEntity.noContent().build();
    }

    /**
     * Toggle space status (activate/deactivate)
     */
    @PatchMapping("/{id}/toggle-status")
    public ResponseEntity<SpaceResponseDto> toggleSpaceStatus(@PathVariable Long id) {
        log.debug("PATCH /api/spaces/{}/toggle-status", id);
        
        Space space = spaceService.toggleSpaceStatus(id);
        List<EquipmentDto> equipment = spaceService.getEquipmentBySpaceId(space.getSpaceId());
        SpaceAvailabilityDto availability = spaceService.getAvailabilityBySpaceId(space.getSpaceId());
        SpaceResponseDto responseDto = SpaceResponseDto.fromEntity(space, equipment, availability);
        
        return ResponseEntity.ok(responseDto);
    }

    /**
     * Update space status
     */
    @PatchMapping("/{id}/status")
    public ResponseEntity<SpaceResponseDto> updateSpaceStatus(
            @PathVariable Long id,
            @RequestParam String status) {
        log.debug("PATCH /api/spaces/{}/status - status: {}", id, status);
        
        Space space = spaceService.updateSpaceStatus(id, status);
        List<EquipmentDto> equipment = spaceService.getEquipmentBySpaceId(space.getSpaceId());
        SpaceAvailabilityDto availability = spaceService.getAvailabilityBySpaceId(space.getSpaceId());
        SpaceResponseDto responseDto = SpaceResponseDto.fromEntity(space, equipment, availability);
        
        return ResponseEntity.ok(responseDto);
    }

    /**
     * Get spaces count for a site
     */
    @GetMapping("/count")
    public ResponseEntity<Long> getSpacesCount(@RequestParam String siteId) {
        log.debug("GET /api/spaces/count - siteId: {}", siteId);
        
        long count = spaceService.countSpacesBySite(siteId);
        return ResponseEntity.ok(count);
    }
}
