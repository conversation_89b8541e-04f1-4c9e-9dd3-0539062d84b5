package com.workeem.workeem_api.business.member.application;

import com.workeem.workeem_api.business.member.domain.Member;
import com.workeem.workeem_api.business.member.domain.MemberType;
import com.workeem.workeem_api.business.member.domain.MemberStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

public interface MemberService {

    /**
     * Get all members for a specific site
     */
    List<Member> getAllMembers(Long siteId);

    /**
     * Get member by ID
     */
    Member getMemberById(Long id);

    /**
     * Get member by email and site
     */
    Optional<Member> getMemberByEmail(Long siteId, String email);

    /**
     * Create a new member
     */
    Member createMember(Member member);

    /**
     * Update an existing member
     */
    Member updateMember(Long memberId, Member member);

    /**
     * Delete a member
     */
    void deleteMember(Long id);

    /**
     * Check if email exists in site
     */
    boolean emailExists(Long siteId, String email);

    /**
     * Count members by site
     */
    long countMembersBySite(Long siteId);

    /**
     * Get all members for a specific site with pagination
     */
    Page<Member> getAllMembers(Long siteId, Pageable pageable);

    /**
     * Get members by status for a specific site with pagination
     */
    Page<Member> getMembersByStatus(Long siteId, MemberStatus status, Pageable pageable);

    /**
     * Search members by name, email, or other fields with optional filters
     */
    Page<Member> searchMembers(Long siteId, String searchTerm, MemberStatus status, MemberType memberType, Long subscriptionId, Pageable pageable);

    /**
     * Get members with filters (status, memberType, subscriptionId) without search
     */
    Page<Member> getMembersWithFilters(Long siteId, MemberStatus status, MemberType memberType, Long subscriptionId, Pageable pageable);
}
