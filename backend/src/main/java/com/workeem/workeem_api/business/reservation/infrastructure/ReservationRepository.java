package com.workeem.workeem_api.business.reservation.infrastructure;

import com.workeem.workeem_api.business.reservation.domain.Reservation;
import com.workeem.workeem_api.business.reservation.domain.ReservationStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ReservationRepository extends JpaRepository<Reservation, Long> {

    /**
     * Find all reservations for a specific site
     */
    List<Reservation> findBySiteId(Long siteId);

    /**
     * Find all reservations for a specific site with relations loaded
     */
    @Query("SELECT r FROM Reservation r " +
           "LEFT JOIN FETCH r.space " +
           "LEFT JOIN FETCH r.member " +
           "WHERE r.siteId = :siteId")
    List<Reservation> findBySiteIdWithRelations(@Param("siteId") Long siteId);

    /**
     * Find reservations by site and status
     */
    List<Reservation> findBySiteIdAndStatus(Long siteId, ReservationStatus status);

    /**
     * Find reservations by site and space
     */
    List<Reservation> findBySiteIdAndSpace_SpaceId(Long siteId, Long spaceId);

    /**
     * Find reservations by site, space and status
     */
    List<Reservation> findBySiteIdAndSpace_SpaceIdAndStatus(Long siteId, Long spaceId, ReservationStatus status);

    /**
     * Find reservations by member
     */
    List<Reservation> findBySiteIdAndMember_MemberId(Long siteId, Long memberId);

    /**
     * Find reservations in a date range
     */
    @Query("SELECT r FROM Reservation r WHERE r.siteId = :siteId AND " +
           "((r.startTime >= :startDate AND r.startTime <= :endDate) OR " +
           "(r.endTime >= :startDate AND r.endTime <= :endDate) OR " +
           "(r.startTime <= :startDate AND r.endTime >= :endDate))")
    List<Reservation> findBySiteIdAndDateRange(@Param("siteId") Long siteId,
                                               @Param("startDate") LocalDateTime startDate,
                                               @Param("endDate") LocalDateTime endDate);

    /**
     * Find reservations in a date range with relations loaded
     */
    @Query("SELECT r FROM Reservation r " +
           "LEFT JOIN FETCH r.space " +
           "LEFT JOIN FETCH r.member " +
           "WHERE r.siteId = :siteId AND " +
           "((r.startTime >= :startDate AND r.startTime <= :endDate) OR " +
           "(r.endTime >= :startDate AND r.endTime <= :endDate) OR " +
           "(r.startTime <= :startDate AND r.endTime >= :endDate))")
    List<Reservation> findBySiteIdAndDateRangeWithRelations(@Param("siteId") Long siteId,
                                                            @Param("startDate") LocalDateTime startDate,
                                                            @Param("endDate") LocalDateTime endDate);

    /**
     * Find reservations by space in a date range
     */
    @Query("SELECT r FROM Reservation r WHERE r.siteId = :siteId AND r.space.spaceId = :spaceId AND " +
           "((r.startTime >= :startDate AND r.startTime <= :endDate) OR " +
           "(r.endTime >= :startDate AND r.endTime <= :endDate) OR " +
           "(r.startTime <= :startDate AND r.endTime >= :endDate))")
    List<Reservation> findBySiteIdAndSpaceIdAndDateRange(@Param("siteId") Long siteId,
                                                         @Param("spaceId") Long spaceId,
                                                         @Param("startDate") LocalDateTime startDate,
                                                         @Param("endDate") LocalDateTime endDate);

    /**
     * Check for conflicting reservations
     */
    @Query("SELECT r FROM Reservation r WHERE r.siteId = :siteId AND r.space.spaceId = :spaceId AND " +
           "r.status NOT IN ('CANCELLED') AND " +
           "((r.startTime < :endTime AND r.endTime > :startTime))")
    List<Reservation> findConflictingReservations(@Param("siteId") Long siteId,
                                                   @Param("spaceId") Long spaceId,
                                                   @Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * Count reservations by site
     */
    long countBySiteId(Long siteId);

    /**
     * Count reservations by site and status
     */
    long countBySiteIdAndStatus(Long siteId, ReservationStatus status);
}
