package com.workeem.workeem_api.business.reservation.application;

import com.workeem.workeem_api.business.member.domain.Member;
import com.workeem.workeem_api.business.member.infrastructure.MemberRepository;
import com.workeem.workeem_api.business.member.application.MemberService;
import com.workeem.workeem_api.business.reservation.domain.Reservation;
import com.workeem.workeem_api.business.reservation.domain.ReservationStatus;
import com.workeem.workeem_api.business.reservation.infrastructure.ReservationRepository;
import com.workeem.workeem_api.business.space.domain.Space;
import com.workeem.workeem_api.business.space.infrastructure.SpaceRepository;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ReservationServiceImpl implements ReservationService {

    private final ReservationRepository reservationRepository;
    private final SpaceRepository spaceRepository;
    private final MemberRepository memberRepository;
    private final MemberService memberService;

    @Override
    @Transactional(readOnly = true)
    public List<Reservation> getAllReservations(Long siteId) {
        log.debug("Fetching all reservations for site: {}", siteId);
        return reservationRepository.findBySiteIdWithRelations(siteId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Reservation> getReservationsWithFilters(Long siteId, Long spaceId, ReservationStatus status,
                                                         LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("Fetching reservations with filters - site: {}, space: {}, status: {}, dateRange: {} to {}", 
                  siteId, spaceId, status, startDate, endDate);

        // Si on a une plage de dates, on l'utilise en priorité
        if (startDate != null && endDate != null) {
            if (spaceId != null) {
                List<Reservation> reservations = reservationRepository.findBySiteIdAndSpaceIdAndDateRange(siteId, spaceId, startDate, endDate);
                if (status != null) {
                    return reservations.stream().filter(r -> r.getStatus() == status).toList();
                }
                return reservations;
            } else {
                List<Reservation> reservations = reservationRepository.findBySiteIdAndDateRangeWithRelations(siteId, startDate, endDate);
                if (status != null) {
                    return reservations.stream().filter(r -> r.getStatus() == status).toList();
                }
                return reservations;
            }
        }

        // Sinon, on utilise les autres filtres
        if (spaceId != null && status != null) {
            return reservationRepository.findBySiteIdAndSpace_SpaceIdAndStatus(siteId, spaceId, status);
        } else if (spaceId != null) {
            return reservationRepository.findBySiteIdAndSpace_SpaceId(siteId, spaceId);
        } else if (status != null) {
            return reservationRepository.findBySiteIdAndStatus(siteId, status);
        } else {
            return reservationRepository.findBySiteIdWithRelations(siteId);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Reservation getReservationById(Long id) {
        log.debug("Fetching reservation with ID: {}", id);
        return reservationRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Reservation with ID " + id + " not found"));
    }

    @Override
    @Transactional(readOnly = true)
    public List<Reservation> getReservationsBySpace(Long siteId, Long spaceId) {
        log.debug("Fetching reservations for space: {} in site: {}", spaceId, siteId);
        return reservationRepository.findBySiteIdAndSpace_SpaceId(siteId, spaceId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Reservation> getReservationsByUser(Long siteId, String userId) {
        log.debug("Fetching reservations for member: {} in site: {}", userId, siteId);
        // Convertir userId en memberId si nécessaire
        try {
            Long memberId = Long.parseLong(userId);
            return reservationRepository.findBySiteIdAndMember_MemberId(siteId, memberId);
        } catch (NumberFormatException e) {
            log.warn("Invalid member ID format: {}", userId);
            return List.of();
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<Reservation> getReservationsInDateRange(Long siteId, LocalDateTime startDate, LocalDateTime endDate) {
        log.debug("Fetching reservations in date range: {} to {} for site: {}", startDate, endDate, siteId);
        return reservationRepository.findBySiteIdAndDateRangeWithRelations(siteId, startDate, endDate);
    }

    @Override
    public Reservation createReservation(Reservation reservation) {
        log.debug("Creating new reservation for space: {} by member: {}",
                  reservation.getSpace() != null ? reservation.getSpace().getSpaceId() : "null",
                  reservation.getMember() != null ? reservation.getMember().getMemberId() : "null");

        // Valider les dates
        if (reservation.getEndTime().isBefore(reservation.getStartTime())) {
            throw new IllegalArgumentException("End time cannot be before start time");
        }

        // Vérifier les conflits
        if (reservation.getSpace() != null && hasConflictingReservations(reservation.getSiteId(),
                reservation.getSpace().getSpaceId(), reservation.getStartTime(), reservation.getEndTime())) {
            throw new IllegalArgumentException("Conflicting reservation exists for this time slot");
        }

        return reservationRepository.save(reservation);
    }

    /**
     * Create reservation with Space and Member IDs
     */
    public Reservation createReservation(Reservation reservation, Long spaceId, Long memberId) {
        log.debug("Creating new reservation for space: {} by member: {}", spaceId, memberId);

        // Récupérer et valider l'espace
        Space space = spaceRepository.findById(spaceId)
                .orElseThrow(() -> new EntityNotFoundException("Space with ID " + spaceId + " not found"));

        // Récupérer et valider le member
        Member member = memberRepository.findById(memberId)
                .orElseThrow(() -> new EntityNotFoundException("Member with ID " + memberId + " not found"));

        // Définir les relations
        reservation.setSpace(space);
        reservation.setMember(member);

        return createReservation(reservation);
    }

    /**
     * Create reservation with automatic member creation if needed
     */
    public Reservation createReservationWithMemberInfo(Reservation reservation, Long spaceId, Long memberId,
                                                       String firstName, String lastName, String email,
                                                       String phone, String company) {
        log.debug("Creating new reservation for space: {} with member info", spaceId);

        // Récupérer et valider l'espace
        Space space = spaceRepository.findById(spaceId)
                .orElseThrow(() -> new EntityNotFoundException("Space with ID " + spaceId + " not found"));

        Member member;
        if (memberId != null) {
            // Member existant
            member = memberRepository.findById(memberId)
                    .orElseThrow(() -> new EntityNotFoundException("Member with ID " + memberId + " not found"));
        } else {
            // Créer un nouveau member
            if (phone == null || phone.trim().isEmpty()) {
                throw new IllegalArgumentException("Phone number is required for new member");
            }

            member = new Member();
            member.setSiteId(reservation.getSiteId());
            member.setFirstName(firstName != null ? firstName : "Member");
            member.setLastName(lastName != null ? lastName : "Externe");
            member.setEmail(email != null ? email : phone + "@temp.com"); // Email temporaire basé sur le téléphone
            member.setPhone(phone);
            member.setCompany(company);
            member.setIsActive(true);

            member = memberService.createMember(member);
            log.debug("Created new member with ID: {}", member.getMemberId());
        }

        // Définir les relations
        reservation.setSpace(space);
        reservation.setMember(member);

        return createReservation(reservation);
    }

    @Override
    public Reservation updateReservation(Long reservationId, Reservation reservation) {
        log.debug("Updating reservation with ID: {}", reservationId);

        Reservation existingReservation = getReservationById(reservationId);

        // Valider les dates
        if (reservation.getEndTime().isBefore(reservation.getStartTime())) {
            throw new IllegalArgumentException("End time cannot be before start time");
        }

        // Vérifier les conflits (en excluant la réservation actuelle)
        if (reservation.getSpace() != null && hasConflictingReservations(reservation.getSiteId(),
                reservation.getSpace().getSpaceId(), reservation.getStartTime(), reservation.getEndTime(), reservationId)) {
            throw new IllegalArgumentException("Conflicting reservation exists for this time slot");
        }

        // Mettre à jour les champs
        if (reservation.getSpace() != null) {
            existingReservation.setSpace(reservation.getSpace());
        }
        if (reservation.getMember() != null) {
            existingReservation.setMember(reservation.getMember());
        }
        existingReservation.setStartTime(reservation.getStartTime());
        existingReservation.setEndTime(reservation.getEndTime());
        existingReservation.setPurpose(reservation.getPurpose());
        existingReservation.setNumberOfPeople(reservation.getNumberOfPeople());
        existingReservation.setNotes(reservation.getNotes());
        existingReservation.setRecurrence(reservation.getRecurrence());
        existingReservation.setTotalCost(reservation.getTotalCost());

        return reservationRepository.save(existingReservation);
    }

    /**
     * Update reservation with Space and Member IDs
     */
    public Reservation updateReservation(Long reservationId, Reservation reservation, Long spaceId, Long memberId) {
        log.debug("Updating reservation with ID: {} for space: {} by member: {}", reservationId, spaceId, memberId);

        // Récupérer et valider l'espace si fourni
        if (spaceId != null) {
            Space space = spaceRepository.findById(spaceId)
                    .orElseThrow(() -> new EntityNotFoundException("Space with ID " + spaceId + " not found"));
            reservation.setSpace(space);
        }

        // Récupérer et valider le member si fourni
        if (memberId != null) {
            Member member = memberRepository.findById(memberId)
                    .orElseThrow(() -> new EntityNotFoundException("Member with ID " + memberId + " not found"));
            reservation.setMember(member);
        }

        return updateReservation(reservationId, reservation);
    }

    @Override
    public Reservation cancelReservation(Long reservationId) {
        log.debug("Cancelling reservation with ID: {}", reservationId);

        Reservation reservation = getReservationById(reservationId);
        reservation.setStatus(ReservationStatus.CANCELLED);

        return reservationRepository.save(reservation);
    }

    @Override
    public Reservation updateReservationStatus(Long reservationId, ReservationStatus status) {
        log.debug("Updating status to {} for reservation with ID: {}", status, reservationId);

        Reservation reservation = getReservationById(reservationId);
        reservation.setStatus(status);

        return reservationRepository.save(reservation);
    }

    @Override
    public void deleteReservation(Long id) {
        log.debug("Deleting reservation with ID: {}", id);

        Reservation reservation = getReservationById(id);
        reservationRepository.delete(reservation);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasConflictingReservations(Long siteId, Long spaceId, LocalDateTime startTime, LocalDateTime endTime) {
        List<Reservation> conflicts = reservationRepository.findConflictingReservations(siteId, spaceId, startTime, endTime);
        return !conflicts.isEmpty();
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasConflictingReservations(Long siteId, Long spaceId, LocalDateTime startTime, LocalDateTime endTime, Long excludeReservationId) {
        List<Reservation> conflicts = reservationRepository.findConflictingReservations(siteId, spaceId, startTime, endTime);
        return conflicts.stream().anyMatch(r -> !r.getReservationId().equals(excludeReservationId));
    }

    @Override
    @Transactional(readOnly = true)
    public long countReservationsBySite(Long siteId) {
        return reservationRepository.countBySiteId(siteId);
    }

    @Override
    @Transactional(readOnly = true)
    public long countReservationsByStatus(Long siteId, ReservationStatus status) {
        return reservationRepository.countBySiteIdAndStatus(siteId, status);
    }
}
