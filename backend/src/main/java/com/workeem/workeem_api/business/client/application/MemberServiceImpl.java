package com.workeem.workeem_api.business.client.application;

import com.workeem.workeem_api.business.client.domain.Member;
import com.workeem.workeem_api.business.client.infrastructure.MemberRepository;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class MemberServiceImpl implements MemberService {

    private final MemberRepository memberRepository;

    @Override
    @Transactional(readOnly = true)
    public List<Member> getAllClients(Long siteId) {
        log.debug("Fetching all clients for site: {}", siteId);
        return memberRepository.findBySiteId(siteId);
    }

    @Override
    @Transactional(readOnly = true)
    public Member getClientById(Long id) {
        log.debug("Fetching client with ID: {}", id);
        return memberRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Client with ID " + id + " not found"));
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Member> getClientByEmail(Long siteId, String email) {
        log.debug("Fetching client by email: {} in site: {}", email, siteId);
        return memberRepository.findBySiteIdAndEmail(siteId, email);
    }

    @Override
    public Member createClient(Member member) {
        log.debug("Creating new client: {} {} for site: {}", 
                  member.getFirstName(), member.getLastName(), member.getSiteId());

        // Vérifier si l'email existe déjà (seulement pour les vrais emails, pas les temporaires)
        if (member.getEmail() != null && !member.getEmail().contains("@temp.com") &&
            emailExists(member.getSiteId(), member.getEmail())) {
            throw new IllegalArgumentException("A client with this email already exists in this site");
        }

        return memberRepository.save(member);
    }

    @Override
    public Member updateClient(Long clientId, Member member) {
        log.debug("Updating client with ID: {}", clientId);

        Member existingMember = getClientById(clientId);

        // Vérifier si l'email existe déjà (en excluant le client actuel)
        Optional<Member> clientWithSameEmail = memberRepository.findBySiteIdAndEmail(member.getSiteId(), member.getEmail());
        if (clientWithSameEmail.isPresent() && !clientWithSameEmail.get().getMemberId().equals(clientId)) {
            throw new IllegalArgumentException("A client with this email already exists in this site");
        }

        // Mettre à jour les champs
        existingMember.setFirstName(member.getFirstName());
        existingMember.setLastName(member.getLastName());
        existingMember.setEmail(member.getEmail());
        existingMember.setPhone(member.getPhone());
        existingMember.setCompany(member.getCompany());
        existingMember.setIsActive(member.getIsActive());

        return memberRepository.save(existingMember);
    }

    @Override
    public void deleteClient(Long id) {
        log.debug("Deleting client with ID: {}", id);

        Member member = getClientById(id);
        memberRepository.delete(member);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean emailExists(Long siteId, String email) {
        return memberRepository.existsBySiteIdAndEmail(siteId, email);
    }

    @Override
    @Transactional(readOnly = true)
    public long countClientsBySite(Long siteId) {
        return memberRepository.countBySiteId(siteId);
    }
}
