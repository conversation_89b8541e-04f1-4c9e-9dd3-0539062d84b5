package com.workeem.workeem_api.business.member.web.dto;

import com.workeem.workeem_api.business.member.domain.Member;
import com.workeem.workeem_api.business.member.domain.MemberType;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberRequestDto {

    @NotBlank(message = "First name is required")
    private String firstName;

    @NotBlank(message = "Last name is required")
    private String lastName;

    @Email(message = "Email should be valid")
    private String email;

    private String phone;

    private String company;

    private MemberType memberType = MemberType.PROFESSIONAL;

    private String studentCode; // Code étudiant pour les étudiants

    private String iceNumber; // Numéro ICE pour les entreprises

    private Long subscriptionId; // ID de l'abonnement

    private Boolean isActive = true;

    /**
     * Convert DTO to Entity
     */
    public Member toEntity(Long siteId) {
        Member member = new Member();
        member.setSiteId(siteId);
        member.setFirstName(this.firstName);
        member.setLastName(this.lastName);
        member.setEmail(this.email);
        member.setPhone(this.phone);
        member.setCompany(this.company);
        member.setMemberType(this.memberType != null ? this.memberType : MemberType.PROFESSIONAL);
        member.setStudentCode(this.studentCode);
        member.setIceNumber(this.iceNumber);
        member.setSubscriptionId(this.subscriptionId);
        member.setIsActive(this.isActive != null ? this.isActive : true);
        return member;
    }
}
